# Google Analytics MCP 一键配置指南

## 🚀 快速开始

### 方法一：完整配置（推荐）
```batch
# 双击运行
setup-analytics-mcp.bat
```

### 方法二：仅认证配置
```batch
# 双击运行
auth-helper.bat
```

## 📁 文件说明

| 文件名 | 用途 | 说明 |
|--------|------|------|
| `setup-analytics-mcp.bat` | 完整配置 | 环境检查+认证+测试 |
| `auth-helper.bat` | 认证助手 | 专门的认证流程 |
| `complete_reauth.py` | 认证核心 | Python认证逻辑 |
| `test_mcp_simple.py` | 功能测试 | MCP功能验证 |

## ✅ 配置成功验证

运行脚本后，如果看到以下信息表示配置成功：

```
SUCCESS: Google Analytics MCP configuration completed!

Now you can use the following features:
- Query account and property information
- Generate data reports
- Get real-time data
- Perform data analysis
```

## 🎯 使用示例

配置成功后，您可以直接询问AI：

**问题**: "昨天有多少用户访问了网站？"
**AI回答**: 自动调用Google Analytics API获取准确数据

**问题**: "过去7天的流量趋势如何？"
**AI回答**: 自动分析多日数据并生成趋势报告

**问题**: "现在有多少用户在线？"
**AI回答**: 调用实时API获取当前在线用户数

## 🔧 认证流程

1. **运行脚本** → 双击bat文件
2. **环境检查** → 自动检查Python和依赖
3. **生成URL** → 自动生成Google认证URL
4. **浏览器认证** → 在浏览器中登录Google账户
5. **授权应用** → 同意访问Google Analytics
6. **复制授权码** → 从浏览器复制授权码
7. **粘贴完成** → 在脚本中粘贴授权码
8. **验证成功** → 自动测试MCP功能

## ❌ 常见问题解决

### 编码问题
如果看到乱码错误，请使用英文版脚本：
- `setup-analytics-mcp.bat`
- `auth-helper.bat`

### 权限不足
```
403 ACCESS_TOKEN_SCOPE_INSUFFICIENT
```
**解决**: 重新运行认证脚本

### API配额限制
```
429 Exhausted property tokens
```
**解决**: 等待24小时或使用其他属性测试

### 授权码无效
```
Invalid authorization code
```
**解决**: 确保完整复制授权码，无多余空格

## 🎉 配置完成后的功能

### 基础查询
- ✅ 获取账户和属性信息
- ✅ 查询用户数据统计
- ✅ 获取会话和页面浏览数据

### 高级分析
- ✅ 流量来源分析
- ✅ 用户行为路径分析
- ✅ 转化漏斗分析
- ✅ 实时数据监控

### AI直接调用
- ✅ `get_account_summaries_analytics()` - 获取账户信息
- ✅ `run_report_analytics()` - 生成数据报告
- ✅ `run_realtime_report_analytics()` - 获取实时数据
- ✅ `get_property_details_analytics()` - 获取属性详情

## 📞 技术支持

1. **详细文档**: `docs/google-analytics-mcp-authentication-guide.md`
2. **快速测试**: 运行 `python test_mcp_simple.py`
3. **重新认证**: 运行 `auth-helper.bat`

---

**配置完成后，Google Analytics MCP 完全可用！AI 可以直接查询和分析您的网站数据！**
