#!/usr/bin/env python3
"""
重新认证Google Analytics API，确保包含所需的权限范围
"""

import os
import json
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow

# Google Analytics API 所需的权限范围
SCOPES = [
    'https://www.googleapis.com/auth/analytics.readonly',
    'https://www.googleapis.com/auth/analytics.manage.users.readonly',
    'https://www.googleapis.com/auth/analytics.edit',
    'https://www.googleapis.com/auth/analytics.manage.users'
]

def main():
    """重新认证并保存凭据"""
    creds = None
    
    # 客户端密钥文件路径
    client_secret_file = "client_secret_677865822475-avm2hbllc79dggr24qondei47gcihnnq.apps.googleusercontent.com.json"
    
    if not os.path.exists(client_secret_file):
        print(f"❌ 客户端密钥文件不存在: {client_secret_file}")
        return
    
    print("🔐 开始 Google Analytics API 认证流程...")
    print(f"📋 权限范围: {SCOPES}")
    
    # 创建认证流程
    flow = InstalledAppFlow.from_client_secrets_file(
        client_secret_file, SCOPES)
    
    # 使用手动授权码流程，设置正确的redirect_uri
    print("🌐 获取认证URL...")
    auth_url, _ = flow.authorization_url(
        prompt='consent',
        access_type='offline',
        include_granted_scopes='true'
    )

    print(f"📱 请访问以下URL进行认证:")
    print(f"{auth_url}")
    print("\n认证完成后，请复制授权码并粘贴到下面:")

    auth_code = input("授权码: ").strip()

    print("🔄 使用授权码获取凭据...")
    flow.fetch_token(code=auth_code)
    creds = flow.credentials
    
    # 保存凭据到默认位置
    creds_dir = os.path.expanduser("~/.config/gcloud")
    os.makedirs(creds_dir, exist_ok=True)
    
    creds_file = os.path.join(creds_dir, "application_default_credentials.json")
    
    # 转换为应用默认凭据格式
    creds_data = {
        "client_id": creds.client_id,
        "client_secret": creds.client_secret,
        "refresh_token": creds.refresh_token,
        "type": "authorized_user",
        "quota_project_id": "sound-silicon-469508-a8"
    }
    
    with open(creds_file, 'w') as f:
        json.dump(creds_data, f, indent=2)
    
    print(f"✅ 认证成功！凭据已保存到: {creds_file}")
    
    # 同时保存到Windows默认位置
    windows_creds_dir = os.path.expanduser("~/AppData/Roaming/gcloud")
    os.makedirs(windows_creds_dir, exist_ok=True)
    
    windows_creds_file = os.path.join(windows_creds_dir, "application_default_credentials.json")
    
    with open(windows_creds_file, 'w') as f:
        json.dump(creds_data, f, indent=2)
    
    print(f"✅ 凭据也已保存到: {windows_creds_file}")
    
    # 测试认证
    print("\n🧪 测试认证...")
    try:
        from google.analytics.admin_v1beta import AnalyticsAdminServiceAsyncClient
        
        # 创建客户端测试
        print("📊 创建 Analytics Admin 客户端...")
        # 这里只是测试导入，实际API调用需要在异步环境中
        print("✅ 客户端创建成功！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n🎉 认证完成！现在可以使用 Google Analytics MCP 了！")
    print("\n📋 下一步:")
    print("1. 重启 Claude Desktop 或 MCP 客户端")
    print("2. 尝试调用 Google Analytics API")

if __name__ == "__main__":
    main()
