@echo off
echo 🚀 启动 Google Analytics MCP Inspector
echo ==========================================

REM 设置环境变量
echo 📋 设置环境变量...
set GOOGLE_APPLICATION_CREDENTIALS=C:\Users\<USER>\AppData\Roaming\gcloud\application_default_credentials.json
set GOOGLE_CLOUD_PROJECT=sound-silicon-469508-a8

REM 显示环境变量
echo ✅ 环境变量已设置:
echo    GOOGLE_APPLICATION_CREDENTIALS=%GOOGLE_APPLICATION_CREDENTIALS%
echo    GOOGLE_CLOUD_PROJECT=%GOOGLE_CLOUD_PROJECT%
echo.

REM 检查凭据文件
if exist "%GOOGLE_APPLICATION_CREDENTIALS%" (
    echo ✅ 凭据文件存在
) else (
    echo ❌ 凭据文件不存在，请先运行: gcloud auth application-default login
    pause
    exit /b 1
)

REM 启动MCP Inspector
echo 🌐 启动 MCP Inspector...
echo 📝 使用说明:
echo    1. Inspector将在浏览器中自动打开
echo    2. 如果没有自动打开，请访问: http://localhost:6274/
echo    3. 在左侧面板查看所有可用工具
echo    4. 点击工具名称进行测试
echo    5. 按 Ctrl+C 停止Inspector
echo.

REM 启动Inspector
mcp dev official-ga-mcp/analytics_mcp/server.py

echo.
echo 👋 MCP Inspector 已停止
pause
