# Google Analytics MCP 使用示例指南

## 🎯 概述

本指南提供了Google Analytics MCP配置成功后的各种使用示例，帮助您充分利用AI驱动的数据分析功能。

## 📊 基础数据查询

### 1. 用户数据查询

**询问示例**：
```
"昨天有多少用户访问了我的网站？"
"过去7天的活跃用户数是多少？"
"本月的用户增长情况如何？"
```

**AI会自动调用**：
```python
run_report_analytics(
    property_id="your_property_id",
    date_ranges=[{"start_date": "yesterday", "end_date": "yesterday"}],
    dimensions=["date"],
    metrics=["activeUsers", "sessions"]
)
```

**预期回答格式**：
```
根据您的Google Analytics数据：

📅 昨天 (2025-08-19)
👥 活跃用户: 3人
🔄 会话数: 6次
📄 页面浏览: 5次

相比前一天，用户数增长了50%。
```

### 2. 流量趋势分析

**询问示例**：
```
"过去30天的流量趋势如何？"
"哪一天的访问量最高？"
"流量有什么规律吗？"
```

**AI分析结果**：
- 📈 趋势图表描述
- 🔥 流量高峰日期
- 📉 流量低谷分析
- 💡 规律性发现

### 3. 页面性能分析

**询问示例**：
```
"哪些页面最受欢迎？"
"用户在哪个页面停留时间最长？"
"跳出率最高的页面是哪些？"
```

## 🌍 流量来源分析

### 1. 流量来源分布

**询问示例**：
```
"主要的流量来源是什么？"
"有多少流量来自搜索引擎？"
"社交媒体带来了多少访问？"
```

**AI会调用**：
```python
run_report_analytics(
    property_id="your_property_id",
    date_ranges=[{"start_date": "30daysAgo", "end_date": "yesterday"}],
    dimensions=["sourceMedium"],
    metrics=["sessions", "activeUsers"]
)
```

### 2. 地理位置分析

**询问示例**：
```
"访问者主要来自哪些国家？"
"中国用户的访问情况如何？"
"不同地区的用户行为有什么差异？"
```

### 3. 设备和浏览器分析

**询问示例**：
```
"移动端和桌面端的访问比例？"
"用户主要使用什么浏览器？"
"不同设备的转化率如何？"
```

## ⚡ 实时数据监控

### 1. 当前在线用户

**询问示例**：
```
"现在有多少用户在线？"
"实时访问者在看什么页面？"
"当前最活跃的流量来源是什么？"
```

**AI会调用**：
```python
run_realtime_report_analytics(
    property_id="your_property_id",
    dimensions=["country", "deviceCategory"],
    metrics=["activeUsers"]
)
```

### 2. 实时事件监控

**询问示例**：
```
"用户现在在执行什么操作？"
"有人在购买产品吗？"
"实时转化情况如何？"
```

## 📈 高级分析查询

### 1. 用户行为路径

**询问示例**：
```
"用户通常的浏览路径是什么？"
"从首页到购买页面的转化漏斗如何？"
"用户在哪个步骤流失最多？"
```

### 2. 转化分析

**询问示例**：
```
"转化率最高的页面是哪些？"
"不同流量来源的转化效果如何？"
"移动端和桌面端的转化率差异？"
```

### 3. 内容性能分析

**询问示例**：
```
"哪些内容最受欢迎？"
"用户对新发布的内容反应如何？"
"内容的参与度趋势怎样？"
```

## 🎯 业务洞察查询

### 1. 营销效果评估

**询问示例**：
```
"上次营销活动的效果如何？"
"付费广告带来了多少流量？"
"哪个营销渠道的ROI最高？"
```

### 2. 用户留存分析

**询问示例**：
```
"新用户的留存率如何？"
"回访用户的行为特征是什么？"
"用户生命周期价值如何？"
```

### 3. 季节性趋势分析

**询问示例**：
```
"网站流量有季节性规律吗？"
"节假日对访问量的影响如何？"
"同比去年的增长情况？"
```

## 🔍 自定义分析

### 1. 特定时间段分析

**询问示例**：
```
"分析一下上周的数据表现"
"对比本月和上月的关键指标"
"分析特定日期范围的用户行为"
```

### 2. 细分用户群体

**询问示例**：
```
"新用户和老用户的行为差异？"
"不同年龄段用户的偏好？"
"高价值用户的特征是什么？"
```

### 3. 竞争对比分析

**询问示例**：
```
"我们的表现相比行业平均水平如何？"
"关键指标的基准对比？"
"需要改进的领域有哪些？"
```

## 📊 报告生成

### 1. 定期报告

**询问示例**：
```
"生成本周的网站表现报告"
"创建月度流量分析报告"
"制作季度业务洞察报告"
```

### 2. 专题报告

**询问示例**：
```
"分析移动端用户体验报告"
"电商转化漏斗分析报告"
"内容营销效果评估报告"
```

## 🚀 高级功能示例

### 1. 预测分析

**询问示例**：
```
"基于历史数据预测下月流量"
"预测用户增长趋势"
"分析可能的流量波动"
```

### 2. 异常检测

**询问示例**：
```
"检测最近的流量异常"
"分析转化率突然下降的原因"
"识别不寻常的用户行为模式"
```

### 3. 优化建议

**询问示例**：
```
"基于数据给出网站优化建议"
"如何提高用户参与度？"
"改善转化率的具体措施？"
```

## 💡 最佳实践

### 1. 提问技巧
- **具体明确**：指定时间范围和指标
- **上下文清晰**：提供业务背景信息
- **逐步深入**：从概览到细节分析

### 2. 数据解读
- **关注趋势**：不只看绝对数值
- **对比分析**：同比、环比、基准对比
- **行动导向**：从数据到决策建议

### 3. 持续监控
- **定期检查**：建立数据监控习惯
- **关键指标**：聚焦核心业务指标
- **及时响应**：快速响应数据变化

## 🎉 成功案例

### 案例1：电商网站优化
**问题**：转化率下降
**查询**：分析用户购买路径和流失点
**结果**：发现购物车页面加载慢，优化后转化率提升30%

### 案例2：内容营销效果
**问题**：内容参与度低
**查询**：分析不同内容类型的用户反应
**结果**：调整内容策略，用户停留时间增加50%

### 案例3：移动端体验改善
**问题**：移动端跳出率高
**查询**：对比移动端和桌面端用户行为
**结果**：优化移动端界面，跳出率降低25%

## 📞 获取更多帮助

如果您需要更复杂的分析或遇到问题：
1. 查看 [故障排除指南](troubleshooting-guide.md)
2. 参考 [完整配置指南](complete-setup-guide.md)
3. 访问 [GitHub Issues](https://github.com/googleanalytics/google-analytics-mcp/issues)

**开始探索您的数据，发现更多商业洞察！** 🚀
