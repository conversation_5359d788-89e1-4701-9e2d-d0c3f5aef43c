#!/bin/bash

# Gemini CLI Google Analytics MCP 快速配置脚本
# 使用方法: ./quick-setup-gemini-mcp.sh

set -e

echo "🚀 开始配置 Gemini CLI Google Analytics MCP..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查必要工具
check_requirements() {
    echo -e "${BLUE}📋 检查系统要求...${NC}"
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ Node.js 未安装，请先安装 Node.js${NC}"
        exit 1
    fi
    
    # 检查 Python
    if ! command -v python3 &> /dev/null; then
        echo -e "${RED}❌ Python3 未安装，请先安装 Python3${NC}"
        exit 1
    fi
    
    # 检查 pip
    if ! command -v pip3 &> /dev/null; then
        echo -e "${RED}❌ pip3 未安装，请先安装 pip3${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 系统要求检查通过${NC}"
}

# 安装依赖
install_dependencies() {
    echo -e "${BLUE}📦 安装依赖包...${NC}"
    
    # 安装 Gemini CLI
    echo "安装 Gemini CLI..."
    npm install -g @google/generative-ai-cli || {
        echo -e "${RED}❌ Gemini CLI 安装失败${NC}"
        exit 1
    }
    
    # 安装 Google Analytics MCP
    echo "安装 Google Analytics MCP..."
    pip3 install google-analytics-mcp || {
        echo -e "${RED}❌ Google Analytics MCP 安装失败${NC}"
        exit 1
    }
    
    echo -e "${GREEN}✅ 依赖包安装完成${NC}"
}

# 创建配置目录
create_config_dirs() {
    echo -e "${BLUE}📁 创建配置目录...${NC}"
    
    CONFIG_DIR="$HOME/.config/gemini-cli"
    mkdir -p "$CONFIG_DIR"
    
    echo -e "${GREEN}✅ 配置目录创建完成: $CONFIG_DIR${NC}"
}

# 获取用户输入
get_user_input() {
    echo -e "${BLUE}📝 请提供配置信息...${NC}"
    
    # Google Cloud 项目 ID
    read -p "请输入您的 Google Cloud 项目 ID: " PROJECT_ID
    if [ -z "$PROJECT_ID" ]; then
        echo -e "${RED}❌ 项目 ID 不能为空${NC}"
        exit 1
    fi
    
    # 服务账号密钥文件路径
    read -p "请输入服务账号密钥文件的完整路径: " CREDENTIALS_PATH
    if [ ! -f "$CREDENTIALS_PATH" ]; then
        echo -e "${RED}❌ 服务账号密钥文件不存在: $CREDENTIALS_PATH${NC}"
        exit 1
    fi
    
    # Gemini API Key
    read -p "请输入您的 Gemini API Key: " GEMINI_API_KEY
    if [ -z "$GEMINI_API_KEY" ]; then
        echo -e "${RED}❌ Gemini API Key 不能为空${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 用户输入收集完成${NC}"
}

# 创建 MCP 配置文件
create_mcp_config() {
    echo -e "${BLUE}⚙️ 创建 MCP 配置文件...${NC}"
    
    MCP_CONFIG_FILE="$HOME/.config/gemini-cli/mcp-config.json"
    
    cat > "$MCP_CONFIG_FILE" << EOF
{
  "mcpServers": {
    "google-analytics": {
      "command": "google-analytics-mcp",
      "args": [],
      "env": {
        "GOOGLE_APPLICATION_CREDENTIALS": "$CREDENTIALS_PATH",
        "GOOGLE_CLOUD_PROJECT": "$PROJECT_ID"
      }
    }
  }
}
EOF
    
    echo -e "${GREEN}✅ MCP 配置文件创建完成: $MCP_CONFIG_FILE${NC}"
}

# 创建 Gemini CLI 配置文件
create_gemini_config() {
    echo -e "${BLUE}⚙️ 创建 Gemini CLI 配置文件...${NC}"
    
    GEMINI_CONFIG_FILE="$HOME/.config/gemini-cli/config.json"
    
    cat > "$GEMINI_CONFIG_FILE" << EOF
{
  "apiKey": "$GEMINI_API_KEY",
  "model": "gemini-pro",
  "mcp": {
    "enabled": true,
    "configFile": "$HOME/.config/gemini-cli/mcp-config.json"
  }
}
EOF
    
    echo -e "${GREEN}✅ Gemini CLI 配置文件创建完成: $GEMINI_CONFIG_FILE${NC}"
}

# 设置环境变量
setup_environment() {
    echo -e "${BLUE}🌍 设置环境变量...${NC}"
    
    # 检查 shell 类型
    if [ -n "$ZSH_VERSION" ]; then
        SHELL_RC="$HOME/.zshrc"
    elif [ -n "$BASH_VERSION" ]; then
        SHELL_RC="$HOME/.bashrc"
    else
        SHELL_RC="$HOME/.profile"
    fi
    
    # 添加环境变量到 shell 配置文件
    echo "" >> "$SHELL_RC"
    echo "# Google Analytics MCP 配置" >> "$SHELL_RC"
    echo "export GOOGLE_APPLICATION_CREDENTIALS=\"$CREDENTIALS_PATH\"" >> "$SHELL_RC"
    echo "export GOOGLE_CLOUD_PROJECT=\"$PROJECT_ID\"" >> "$SHELL_RC"
    
    # 立即设置环境变量
    export GOOGLE_APPLICATION_CREDENTIALS="$CREDENTIALS_PATH"
    export GOOGLE_CLOUD_PROJECT="$PROJECT_ID"
    
    echo -e "${GREEN}✅ 环境变量设置完成${NC}"
    echo -e "${YELLOW}⚠️ 请重新启动终端或运行 'source $SHELL_RC' 使环境变量生效${NC}"
}

# 验证配置
verify_setup() {
    echo -e "${BLUE}🔍 验证配置...${NC}"
    
    # 检查 Gemini CLI
    if ! command -v gemini &> /dev/null; then
        echo -e "${RED}❌ Gemini CLI 未正确安装${NC}"
        return 1
    fi
    
    # 检查 Google Analytics MCP
    if ! command -v google-analytics-mcp &> /dev/null; then
        echo -e "${RED}❌ Google Analytics MCP 未正确安装${NC}"
        return 1
    fi
    
    # 检查配置文件
    if [ ! -f "$HOME/.config/gemini-cli/config.json" ]; then
        echo -e "${RED}❌ Gemini CLI 配置文件不存在${NC}"
        return 1
    fi
    
    if [ ! -f "$HOME/.config/gemini-cli/mcp-config.json" ]; then
        echo -e "${RED}❌ MCP 配置文件不存在${NC}"
        return 1
    fi
    
    echo -e "${GREEN}✅ 配置验证通过${NC}"
}

# 创建测试脚本
create_test_script() {
    echo -e "${BLUE}📝 创建测试脚本...${NC}"
    
    TEST_SCRIPT="$HOME/test-gemini-mcp.sh"
    
    cat > "$TEST_SCRIPT" << 'EOF'
#!/bin/bash

echo "🧪 测试 Gemini CLI Google Analytics MCP 配置..."

# 设置环境变量
source ~/.bashrc 2>/dev/null || source ~/.zshrc 2>/dev/null || source ~/.profile 2>/dev/null

# 测试基础连接
echo "1. 测试 MCP 服务器状态..."
gemini mcp status

echo "2. 测试 MCP 工具列表..."
gemini mcp list-tools

echo "3. 测试 Google Analytics 查询..."
gemini "使用 Google Analytics MCP 获取我的账户摘要"

echo "✅ 测试完成！"
EOF
    
    chmod +x "$TEST_SCRIPT"
    
    echo -e "${GREEN}✅ 测试脚本创建完成: $TEST_SCRIPT${NC}"
}

# 显示使用说明
show_usage_instructions() {
    echo -e "${GREEN}🎉 配置完成！${NC}"
    echo ""
    echo -e "${BLUE}📖 使用说明：${NC}"
    echo "1. 重新启动终端或运行以下命令使环境变量生效："
    echo -e "   ${YELLOW}source ~/.bashrc${NC} (或 ~/.zshrc 或 ~/.profile)"
    echo ""
    echo "2. 运行测试脚本验证配置："
    echo -e "   ${YELLOW}~/test-gemini-mcp.sh${NC}"
    echo ""
    echo "3. 开始使用 Gemini CLI 与 Google Analytics MCP："
    echo -e "   ${YELLOW}gemini \"分析我的网站最近30天的数据\"${NC}"
    echo ""
    echo -e "${BLUE}📚 更多示例：${NC}"
    echo "• gemini \"获取我的 Google Analytics 账户摘要\""
    echo "• gemini \"分析流量来源和用户行为\""
    echo "• gemini \"生成月度分析报告\""
    echo ""
    echo -e "${GREEN}🔗 相关文档：${NC}"
    echo "• 详细配置指南: gemini-cli-mcp-setup-guide.md"
    echo "• 故障排除: troubleshooting-guide.md"
}

# 主函数
main() {
    echo -e "${GREEN}🚀 Gemini CLI Google Analytics MCP 快速配置工具${NC}"
    echo "=================================================="
    echo ""
    
    check_requirements
    install_dependencies
    create_config_dirs
    get_user_input
    create_mcp_config
    create_gemini_config
    setup_environment
    verify_setup
    create_test_script
    show_usage_instructions
    
    echo ""
    echo -e "${GREEN}✨ 配置完成！现在您可以在 Gemini CLI 中使用 Google Analytics MCP 了！${NC}"
}

# 运行主函数
main "$@"
