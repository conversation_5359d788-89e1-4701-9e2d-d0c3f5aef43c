# MCP Inspector 成功运行完整教程

## 🎯 教程概述

本教程基于实际成功运行经验，详细介绍如何使用MCP Inspector测试Google Analytics MCP工具。通过本教程，您将学会完整的MCP Inspector操作流程，从启动到成功获取Google Analytics数据。

---

## ✅ 验证成功的运行结果

**实际测试结果确认：**
- ✅ MCP Inspector成功启动并运行在 http://localhost:6274/
- ✅ 成功连接到Google Analytics MCP服务器
- ✅ 发现并列出6个可用的Google Analytics工具
- ✅ 成功执行get_account_summaries工具
- ✅ 获取到4个Google Analytics账户的完整数据
- ✅ 返回结构化JSON数据，验证API连接正常

---

## 🚀 Step 1: 环境准备

### 1.1 设置环境变量
```powershell
# 在PowerShell中设置环境变量
$env:GOOGLE_APPLICATION_CREDENTIALS="C:\Users\<USER>\AppData\Roaming\gcloud\application_default_credentials.json"
$env:GOOGLE_CLOUD_PROJECT="sound-silicon-469508-a8"
```

### 1.2 验证环境配置
```powershell
# 验证环境变量
echo $env:GOOGLE_APPLICATION_CREDENTIALS
echo $env:GOOGLE_CLOUD_PROJECT

# 验证认证文件存在
Test-Path $env:GOOGLE_APPLICATION_CREDENTIALS
```

**预期结果：**
- 环境变量正确显示路径和项目ID
- 认证文件存在且可访问

---

## 🌐 Step 2: 启动MCP Inspector

### 2.1 启动命令
```bash
# 在项目根目录执行
mcp dev official-ga-mcp/analytics_mcp/server.py
```

### 2.2 成功启动的标志
看到以下输出表示启动成功：
```
Starting MCP inspector...
⚙️ Proxy server listening on localhost:6277
🔑 Session token: 16ab39afb9a081059fc10408f43b20970f0f486a46bcd1a567f936e60373768a
🚀 MCP Inspector is up and running at:
   http://localhost:6274/?MCP_PROXY_AUTH_TOKEN=16ab39afb9a081059fc10408f43b20970f0f486a46bcd1a567f936e60373768a
🌐 Opening browser...
```

**关键信息：**
- **代理服务器端口：** 6277
- **Web界面端口：** 6274
- **认证令牌：** 自动生成的长字符串
- **完整访问URL：** 包含认证令牌的完整地址

---

## 🖥️ Step 3: 访问Web界面

### 3.1 浏览器访问
- **方式1：** 浏览器会自动打开（推荐）
- **方式2：** 手动复制完整URL到浏览器
- **方式3：** 访问 http://localhost:6274/ 然后手动输入认证令牌

### 3.2 界面验证
成功访问后应该看到：
- **标题：** MCP Inspector v0.16.5
- **连接状态：** Connected（绿色）
- **配置信息：** 
  - Transport Type: STDIO
  - Command: google-analytics-mcp
  - Arguments: (空)

---

## 🔧 Step 4: 连接MCP服务器

### 4.1 自动连接验证
如果使用`mcp dev`启动，连接应该是自动的。验证连接成功的标志：
- **连接状态显示：** "Connected"
- **按钮状态：** 显示"Restart"和"Disconnect"按钮
- **标签页出现：** Resources, Prompts, Tools, Ping等标签页
- **History显示：** "1. initialize"

### 4.2 手动连接（如需要）
如果连接失败，可以手动配置：
1. 确认Command字段为：`google-analytics-mcp`
2. 确认Arguments字段为空
3. 点击"Connect"按钮

---

## 📋 Step 5: 获取工具列表

### 5.1 切换到Tools标签页
1. 点击界面上方的"Tools"标签页
2. 点击"List Tools"按钮

### 5.2 验证工具列表
成功后应该看到6个Google Analytics MCP工具：

**管理类工具：**
- `get_account_summaries` - 获取账户摘要
- `get_property_details` - 获取属性详情
- `get_custom_dimensions_and_metrics` - 获取自定义维度和指标
- `list_google_ads_links` - 列出Google Ads链接

**报告类工具：**
- `run_report` - 运行标准数据报告
- `run_realtime_report` - 运行实时数据报告

---

## 🧪 Step 6: 测试工具执行

### 6.1 选择测试工具
推荐从最简单的工具开始：
1. 点击左侧的`get_account_summaries`工具
2. 查看工具描述和输出模式

### 6.2 执行工具
1. 点击"Run Tool"按钮
2. 观察按钮状态变为"Running..."
3. 等待执行完成

### 6.3 查看结果
成功执行后会显示：
- **状态：** "Tool Result: Success"（绿色）
- **结构化内容：** 完整的JSON数据
- **验证标志：** "✓ Valid according to output schema"

---

## 📊 Step 7: 分析返回数据

### 7.1 数据结构
成功的get_account_summaries调用返回：
```json
{
  "result": [
    {
      "name": "accountSummaries/*********",
      "account": "accounts/*********", 
      "display_name": "kickstarter",
      "property_summaries": [...]
    },
    // ... 更多账户
  ]
}
```

### 7.2 实际获取的账户数据
**验证获取到的4个账户：**
1. **kickstarter** (*********) - 2个属性
2. **zhuhaibeier** (*********) - 1个属性  
3. **zhuhaibeier** (*********) - 1个属性
4. **Demo Account** (********) - 3个属性

---

## 🔍 Step 8: 测试其他工具

### 8.1 测试带参数的工具
选择`run_report`工具进行高级测试：

**示例参数：**
```json
{
  "property_id": "*********",
  "date_ranges": [{"start_date": "30daysAgo", "end_date": "yesterday"}],
  "dimensions": ["country"],
  "metrics": ["sessions", "totalUsers"]
}
```

### 8.2 测试实时报告
选择`run_realtime_report`工具：

**示例参数：**
```json
{
  "property_id": "*********", 
  "dimensions": ["country", "deviceCategory"],
  "metrics": ["activeUsers"]
}
```

---

## 🛠️ 故障排除指南

### 问题1: 端口被占用
**错误现象：**
```
❌  Proxy Server PORT IS IN USE at port 6277 ❌
❌  MCP Inspector PORT IS IN USE at http://localhost:6274 ❌
```

**解决方案：**
```bash
# 查找占用端口的进程
netstat -ano | findstr :6277
netstat -ano | findstr :6274

# 终止进程（替换PID为实际进程ID）
taskkill /PID <PID> /F

# 重新启动Inspector
mcp dev official-ga-mcp/analytics_mcp/server.py
```

### 问题2: 认证失败
**错误现象：** 工具调用返回认证错误

**解决方案：**
```bash
# 重新设置环境变量
$env:GOOGLE_APPLICATION_CREDENTIALS="C:\Users\<USER>\AppData\Roaming\gcloud\application_default_credentials.json"
$env:GOOGLE_CLOUD_PROJECT="sound-silicon-469508-a8"

# 验证认证
gcloud auth application-default print-access-token
```

### 问题3: 工具列表为空
**可能原因：**
- MCP服务器未正确启动
- 环境变量未设置
- 认证配置错误

**解决方案：**
1. 重新启动MCP Inspector
2. 验证环境变量设置
3. 检查认证文件路径

---

## ✅ 成功验证清单

完成以下检查确保MCP Inspector正常工作：

- [ ] MCP Inspector成功启动
- [ ] Web界面可正常访问
- [ ] 连接状态显示"Connected"
- [ ] 工具列表显示6个Google Analytics工具
- [ ] get_account_summaries工具执行成功
- [ ] 返回真实的Google Analytics账户数据
- [ ] JSON数据格式验证通过
- [ ] History显示工具调用记录

---

## 🎉 总结

通过本教程，您已经成功：

1. **掌握了MCP Inspector的完整使用流程**
2. **验证了Google Analytics MCP的正常工作**
3. **获取了真实的Google Analytics数据**
4. **了解了常见问题的解决方法**

现在您可以：
- 🧪 测试所有Google Analytics MCP工具
- 📊 验证API调用和参数格式
- 🔍 调试MCP服务器问题
- 🚀 为Gemini CLI集成做好准备

**下一步建议：**
- 测试更多复杂的报告工具
- 尝试不同的参数组合
- 为Gemini CLI配置MCP连接

---

## 📸 关键界面截图说明

### 启动成功界面
```
🚀 MCP Inspector is up and running at:
   http://localhost:6274/?MCP_PROXY_AUTH_TOKEN=16ab39afb9a081059fc10408f43b20970f0f486a46bcd1a567f936e60373768a
🌐 Opening browser...
```

### Web界面关键元素
- **左上角：** MCP Inspector v0.16.5 标题
- **配置区域：** Transport Type: STDIO, Command: google-analytics-mcp
- **连接状态：** Connected (绿色显示)
- **标签页：** Resources | Prompts | Tools | Ping | Sampling | Elicitations | Roots | Auth

### 工具执行成功界面
- **工具名称：** get_account_summaries
- **执行状态：** Tool Result: Success (绿色)
- **数据验证：** ✓ Valid according to output schema
- **JSON数据：** 完整的账户信息结构化显示

### History记录
```
3. tools/call ▶
2. tools/list ▶
1. initialize ▶
```

---

## 🔗 相关文档链接

- [MCP Inspector官方文档](https://modelcontextprotocol.io/docs/tools/inspector)
- [Google Analytics Reporting API文档](https://developers.google.com/analytics/devguides/reporting/data/v1)
- [MCP协议规范](https://modelcontextprotocol.io/)

---

## 📞 技术支持

如果在使用过程中遇到问题：

1. **检查环境变量设置**
2. **验证Google Cloud认证**
3. **确认端口未被占用**
4. **查看MCP服务器日志输出**
5. **参考故障排除指南**

**记住：** 成功的关键是使用 `mcp dev` 命令启动，而不是手动配置连接！
