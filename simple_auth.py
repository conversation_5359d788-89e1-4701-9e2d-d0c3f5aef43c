#!/usr/bin/env python3
"""
简单的Google Analytics API认证脚本
使用现有的认证令牌并刷新权限
"""

import os
import json
import requests
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials

def refresh_credentials():
    """刷新现有的认证凭据"""
    
    # 读取现有的认证文件
    creds_file = r"C:\Users\<USER>\AppData\Roaming\gcloud\application_default_credentials.json"
    
    if not os.path.exists(creds_file):
        print(f"❌ 认证文件不存在: {creds_file}")
        return False
    
    print("📖 读取现有认证文件...")
    with open(creds_file, 'r') as f:
        creds_data = json.load(f)
    
    print(f"📋 当前认证信息:")
    print(f"   Client ID: {creds_data.get('client_id', 'N/A')}")
    print(f"   Project ID: {creds_data.get('quota_project_id', 'N/A')}")
    print(f"   Type: {creds_data.get('type', 'N/A')}")
    
    # 创建凭据对象
    creds = Credentials(
        token=None,  # 将被刷新
        refresh_token=creds_data.get('refresh_token'),
        token_uri=creds_data.get('token_uri', 'https://oauth2.googleapis.com/token'),
        client_id=creds_data.get('client_id'),
        client_secret=creds_data.get('client_secret')
        # 不指定scopes，使用原有的权限范围
    )
    
    print("🔄 刷新访问令牌...")
    try:
        creds.refresh(Request())
        print("✅ 令牌刷新成功！")
        
        # 更新认证文件
        updated_creds_data = {
            "client_id": creds.client_id,
            "client_secret": creds.client_secret,
            "refresh_token": creds.refresh_token,
            "type": "authorized_user",
            "quota_project_id": creds_data.get('quota_project_id', 'sound-silicon-469508-a8'),
            "universe_domain": "googleapis.com"
        }
        
        with open(creds_file, 'w') as f:
            json.dump(updated_creds_data, f, indent=2)
        
        print(f"✅ 认证文件已更新: {creds_file}")
        return True
        
    except Exception as e:
        print(f"❌ 令牌刷新失败: {e}")
        return False

def test_analytics_api():
    """测试Google Analytics API访问"""
    print("\n🧪 测试Google Analytics API访问...")
    
    try:
        from google.analytics.admin_v1beta import AnalyticsAdminServiceClient
        from google.analytics.data_v1beta import BetaAnalyticsDataClient
        
        print("📊 创建Analytics客户端...")
        
        # 测试Admin API
        admin_client = AnalyticsAdminServiceClient()
        print("✅ Admin API客户端创建成功")
        
        # 测试Data API  
        data_client = BetaAnalyticsDataClient()
        print("✅ Data API客户端创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔐 Google Analytics API 认证刷新工具")
    print("=" * 50)
    
    # 刷新认证
    if refresh_credentials():
        print("\n✅ 认证刷新成功！")
        
        # 测试API访问
        if test_analytics_api():
            print("\n🎉 Google Analytics API 已准备就绪！")
            print("\n📋 现在可以:")
            print("1. 使用MCP客户端调用Google Analytics API")
            print("2. 查询账户和属性信息")
            print("3. 生成数据报告")
        else:
            print("\n⚠️ API测试失败，可能需要重新完整认证")
    else:
        print("\n❌ 认证刷新失败")
        print("💡 建议:")
        print("1. 检查网络连接")
        print("2. 确认Google账户权限")
        print("3. 可能需要重新完整认证")

if __name__ == "__main__":
    main()
