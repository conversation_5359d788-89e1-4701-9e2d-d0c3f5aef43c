# Google Analytics MCP 认证配置快速指南

## 🎯 快速开始

### 一键配置（推荐）
```batch
# 双击运行完整配置
setup-google-analytics-mcp.bat
```

### 仅重新认证
```batch  
# 双击运行认证辅助
manual-auth-helper.bat
```

## 📁 文件说明

| 文件 | 用途 | 说明 |
|------|------|------|
| `setup-google-analytics-mcp.bat` | 完整配置 | 环境检查+认证+测试 |
| `manual-auth-helper.bat` | 认证辅助 | 专门的认证流程 |
| `complete_reauth.py` | 认证脚本 | Python认证核心逻辑 |
| `docs/google-analytics-mcp-authentication-guide.md` | 详细文档 | 完整配置指南 |

## 🔧 认证流程

1. **运行脚本** → 选择合适的bat文件双击运行
2. **浏览器认证** → 自动打开Google认证页面  
3. **授权应用** → 登录并授权访问Analytics
4. **复制授权码** → 从浏览器复制授权码
5. **粘贴完成** → 在脚本中粘贴授权码
6. **验证成功** → 自动测试MCP功能

## ✅ 成功标志

- ✅ 认证文件已更新
- ✅ API客户端创建成功
- ✅ MCP工具调用正常
- ✅ 数据查询返回结果

## 🚀 使用示例

配置成功后，AI可以直接回答：

**问**: "昨天有多少用户？"
**答**: AI自动调用 `run_report_analytics()` 获取数据

**问**: "过去7天的流量趋势？"  
**答**: AI自动分析多日数据并生成趋势报告

**问**: "现在有多少用户在线？"
**答**: AI调用 `run_realtime_report_analytics()` 获取实时数据

## ❌ 常见问题

### 权限不足错误
```
403 ACCESS_TOKEN_SCOPE_INSUFFICIENT
```
**解决**: 运行 `manual-auth-helper.bat` 重新认证

### 授权码无效
```
Invalid authorization code
```
**解决**: 确保完整复制授权码，无多余空格

### API配额限制
```
429 Exhausted property tokens
```
**解决**: 等待24小时或使用其他属性测试

## 📞 技术支持

1. **查看详细文档**: `docs/google-analytics-mcp-authentication-guide.md`
2. **检查错误日志**: 脚本会显示详细错误信息
3. **重新运行脚本**: 大多数问题可通过重新认证解决

---

**配置完成后，Google Analytics MCP 将完全可用！AI 助手可以直接查询和分析您的网站数据！**
