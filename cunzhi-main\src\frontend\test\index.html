<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>寸止 - 组件样式测试环境</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    }
    
    #app {
      width: 100%;
      height: 100vh;
    }
    
    /* 加载动画 */
    .loading {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100vh;
      font-size: 1.2rem;
      color: #666;
    }
    
    .loading::after {
      content: '';
      width: 20px;
      height: 20px;
      border: 2px solid #14b8a6;
      border-top: 2px solid transparent;
      border-radius: 50%;
      margin-left: 10px;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div id="app">
    <div class="loading">
      正在加载测试环境...
    </div>
  </div>
  
  <script type="module" src="./main.ts"></script>
</body>
</html>
