{"$schema": "https://schema.tauri.app/config/2.0.0", "productName": "寸止", "version": "0.3.8", "identifier": "com.imhuso.cunzhi", "build": {"beforeDevCommand": "pnpm dev", "beforeBuildCommand": "pnpm build", "frontendDist": "./dist", "devUrl": "http://localhost:5176"}, "app": {"windows": [{"title": "寸止", "width": 600, "height": 800, "minWidth": 600, "minHeight": 400, "maxWidth": 600, "maxHeight": 1200, "center": true, "visible": true, "alwaysOnTop": true, "decorations": true, "hiddenTitle": true, "transparent": false, "skipTaskbar": false, "resizable": true, "fullscreen": false}], "security": {"csp": null, "capabilities": [{"identifier": "main-capability", "description": "Main window capabilities", "windows": ["main"], "permissions": ["core:event:allow-listen", "core:event:allow-unlisten", "core:event:allow-emit", "core:window:allow-show", "core:window:allow-hide", "core:window:allow-close", "core:window:allow-minimize", "core:window:allow-set-focus", "core:window:allow-set-always-on-top", "core:webview:allow-set-webview-focus", "core:webview:allow-webview-position", "core:webview:allow-webview-size", "shell:allow-open", "updater:allow-check", "updater:allow-download", "updater:allow-install"]}]}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/icon-32.png", "icons/icon-128.png", "icons/icon-256.png", "icons/icon-512.png", "icons/icon.ico", "icons/icon.icns"]}, "plugins": {"updater": {"active": true, "endpoints": ["https://api.github.com/repos/imhuso/cunzhi/releases/latest"], "dialog": false, "pubkey": ""}}}