[package]
name = "google-analytics-mcp"
version = "1.0.0"
edition = "2021"
description = "Google Analytics MCP Server - 封装版本"
authors = ["Your Name <<EMAIL>>"]

[[bin]]
name = "google-analytics-mcp"
path = "src/main.rs"

[dependencies]
tokio = { version = "1.0", features = ["rt-multi-thread", "macros", "process", "fs"] }
anyhow = "1.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
dirs = "5.0"

# 发布版本优化配置
[profile.release]
opt-level = "z"        # 优化体积而非速度
lto = true            # 链接时优化，减少最终二进制大小
codegen-units = 1     # 减少代码生成单元，提高优化效果
panic = "abort"       # 减少panic处理代码
strip = true          # 移除调试符号
