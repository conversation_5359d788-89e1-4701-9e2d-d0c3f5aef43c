@echo off
title Google Analytics MCP Setup Tool

echo.
echo ========================================
echo   Google Analytics MCP Setup Tool
echo ========================================
echo.

:: 检查是否在正确的目录
if not exist "complete_reauth.py" (
    echo ❌ 错误: 请在项目根目录运行此脚本
    echo 当前目录: %CD%
    echo 需要的文件: complete_reauth.py
    pause
    exit /b 1
)

:: 检查Python环境
echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: Python未安装或不在PATH中
    echo 请先安装Python并添加到系统PATH
    pause
    exit /b 1
)
echo ✅ Python环境检查通过

:: 检查虚拟环境
echo.
echo 🔍 检查虚拟环境...
if exist ".venv\Scripts\activate.bat" (
    echo ✅ 发现虚拟环境: .venv
    call .venv\Scripts\activate.bat
) else (
    echo ⚠️  未发现虚拟环境，使用系统Python
)

:: 检查必要的包
echo.
echo 🔍 检查必要的Python包...
python -c "import google.auth, google_auth_oauthlib.flow" >nul 2>&1
if errorlevel 1 (
    echo ❌ 缺少必要的Python包
    echo 正在安装依赖包...
    pip install google-auth google-auth-oauthlib google-analytics-data google-analytics-admin
    if errorlevel 1 (
        echo ❌ 包安装失败
        pause
        exit /b 1
    )
)
echo ✅ Python包检查通过

:: 检查客户端密钥文件
echo.
echo 🔍 检查OAuth客户端密钥文件...
set "CLIENT_SECRET_FILE="
for %%f in (client_secret_*.json) do (
    set "CLIENT_SECRET_FILE=%%f"
    goto :found_secret
)
:found_secret
if "%CLIENT_SECRET_FILE%"=="" (
    echo ❌ 错误: 未找到OAuth客户端密钥文件
    echo 请确保client_secret_*.json文件存在于当前目录
    pause
    exit /b 1
)
echo ✅ 找到客户端密钥文件: %CLIENT_SECRET_FILE%

:: 显示配置信息
echo.
echo 📋 配置信息:
echo    项目目录: %CD%
echo    Python版本: 
python --version
echo    客户端密钥: %CLIENT_SECRET_FILE%
echo.

:: 确认开始配置
echo 🚀 准备开始Google Analytics MCP认证配置
echo.
echo ⚠️  注意事项:
echo    1. 需要Google账户登录权限
echo    2. 需要访问Google Analytics属性的权限  
echo    3. 认证过程需要浏览器操作
echo    4. 请准备好复制粘贴授权码
echo.
set /p confirm="确认开始配置? (y/N): "
if /i not "%confirm%"=="y" (
    echo 配置已取消
    pause
    exit /b 0
)

:: 执行认证脚本
echo.
echo 🔐 开始认证流程...
echo ========================================
python complete_reauth.py
set auth_result=%errorlevel%

echo.
echo ========================================

:: 检查认证结果
if %auth_result%==0 (
    echo ✅ 认证配置完成!
    echo.
    echo 🧪 测试MCP功能...
    
    :: 测试MCP调用
    python -c "
import asyncio
import sys
try:
    print('测试导入Google Analytics包...')
    from google.analytics.admin_v1beta import AnalyticsAdminServiceClient
    from google.analytics.data_v1beta import BetaAnalyticsDataClient
    print('✅ 包导入成功')
    
    print('测试客户端创建...')
    admin_client = AnalyticsAdminServiceClient()
    data_client = BetaAnalyticsDataClient()
    print('✅ 客户端创建成功')
    
    print('🎉 Google Analytics MCP 配置完全成功!')
    print('')
    print('现在可以使用以下功能:')
    print('- 📊 查询账户和属性信息')
    print('- 📈 生成数据报告')
    print('- ⚡ 获取实时数据')
    print('- 🔍 进行数据分析')
    
except Exception as e:
    print(f'❌ 测试失败: {e}')
    sys.exit(1)
"
    
    if errorlevel 1 (
        echo ⚠️  MCP功能测试失败，但认证可能已成功
        echo 请手动测试MCP调用
    ) else (
        echo.
        echo 🎯 配置完全成功! 
        echo Google Analytics MCP 现在可以正常使用了!
    )
    
) else (
    echo ❌ 认证配置失败
    echo 请检查错误信息并重试
)

echo.
echo 📚 更多信息请查看: docs\google-analytics-mcp-authentication-guide.md
echo.
pause
