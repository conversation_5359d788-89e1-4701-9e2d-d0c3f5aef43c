# Google Analytics MCP 完整安装指南

## 概述

本指南详细介绍了如何安装和配置 Google Analytics MCP (Model Context Protocol) 服务器，使其能够与 Gemini CLI 集成，实现对 Google Analytics 数据的智能查询和分析。

## 系统要求

### 必需软件
- **Node.js** (版本 14 或更高)
- **Python** (版本 3.8 或更高)
- **npm** (随 Node.js 安装)
- **pipx** (Python 包管理器)

### 必需凭证
- **Google Cloud 项目** (启用 Analytics API)
- **OAuth 2.0 凭证文件** (JSON 格式)
- **Gemini API Key** (可选，用于 Gemini CLI)

## 安装步骤

### 第一步：安装 Gemini CLI

```bash
# 全局安装 Gemini CLI
npm install -g @google/gemini-cli

# 验证安装
gemini --version
```

**预期输出**: `0.1.22` 或更高版本

### 第二步：安装 pipx

```bash
# 安装 pipx
python -m pip install --user pipx

# 确保 pipx 在 PATH 中
python -m pipx ensurepath
```

**Windows 用户注意**: 可能需要重启终端或重新加载 PATH

### 第三步：准备 Google 凭证

1. **创建 Google Cloud 项目**
   - 访问 [Google Cloud Console](https://console.cloud.google.com/)
   - 创建新项目或选择现有项目
   - 记录项目 ID

2. **启用必要的 API**
   - Google Analytics Reporting API
   - Google Analytics Data API

3. **创建 OAuth 2.0 凭证**
   - 转到 "APIs & Services" > "Credentials"
   - 点击 "Create Credentials" > "OAuth 2.0 Client IDs"
   - 选择 "Desktop application"
   - 下载 JSON 凭证文件

### 第四步：配置 Gemini CLI

创建 Gemini CLI 配置目录和文件：

**Windows:**
```powershell
# 创建配置目录
mkdir $env:USERPROFILE\.gemini

# 创建配置文件
New-Item -Path "$env:USERPROFILE\.gemini\settings.json" -ItemType File
```

**Linux/macOS:**
```bash
# 创建配置目录
mkdir -p ~/.gemini

# 创建配置文件
touch ~/.gemini/settings.json
```

### 第五步：配置 settings.json

编辑 `~/.gemini/settings.json` 文件，添加以下内容：

```json
{
  "selectedAuthType": "gemini-api-key",
  "mcpServers": {
    "analytics-mcp": {
      "command": "python",
      "args": [
        "-m",
        "pipx",
        "run",
        "--spec",
        "git+https://github.com/googleanalytics/google-analytics-mcp.git",
        "google-analytics-mcp"
      ],
      "env": {
        "GOOGLE_APPLICATION_CREDENTIALS": "/path/to/your/credentials.json"
      }
    }
  }
}
```

**重要**: 将 `/path/to/your/credentials.json` 替换为您的实际凭证文件路径

### 第六步：设置环境变量

**Windows PowerShell:**
```powershell
$env:GOOGLE_CLOUD_PROJECT = "your-project-id"
$env:GEMINI_API_KEY = "your-gemini-api-key"
$env:GOOGLE_APPLICATION_CREDENTIALS = "path\to\your\credentials.json"
```

**Linux/macOS:**
```bash
export GOOGLE_CLOUD_PROJECT="your-project-id"
export GEMINI_API_KEY="your-gemini-api-key"
export GOOGLE_APPLICATION_CREDENTIALS="path/to/your/credentials.json"
```

### 第七步：启动和验证

```bash
# 启动 Gemini CLI
gemini
```

**成功标志**:
- 显示 "Using: 1 MCP server (ctrl+t to view)"
- 不显示认证错误
- 可以正常输入消息

## 配置文件详解

### settings.json 结构

```json
{
  "selectedAuthType": "gemini-api-key",  // 认证类型
  "mcpServers": {                        // MCP 服务器配置
    "analytics-mcp": {                   // 服务器名称
      "command": "python",               // 执行命令
      "args": [...],                     // 命令参数
      "env": {                          // 环境变量
        "GOOGLE_APPLICATION_CREDENTIALS": "..."
      }
    }
  },
  "hasSeenIdeIntegrationNudge": true    // IDE 集成提示标志
}
```

### 环境变量说明

| 变量名 | 用途 | 必需性 | 示例值 |
|--------|------|--------|--------|
| `GOOGLE_CLOUD_PROJECT` | Google Cloud 项目 ID | 必需 | `my-analytics-project` |
| `GEMINI_API_KEY` | Gemini API 访问密钥 | 可选 | `AIzaSyC...` |
| `GOOGLE_APPLICATION_CREDENTIALS` | OAuth 凭证文件路径 | 必需 | `/path/to/creds.json` |

## 验证安装

### 1. 检查组件版本

```bash
# 检查 Node.js
node --version

# 检查 Python
python --version

# 检查 Gemini CLI
gemini --version

# 检查 pipx
pipx --version
```

### 2. 测试 MCP 服务器

```bash
# 手动运行 MCP 服务器
python -m pipx run --spec git+https://github.com/googleanalytics/google-analytics-mcp.git google-analytics-mcp --help
```

### 3. 验证配置文件

```bash
# 验证 JSON 格式
python -m json.tool ~/.gemini/settings.json
```

## 故障排除

### 常见错误及解决方案

#### 1. 地理位置限制
**错误**: `User location is not supported for the API use`
**解决**: 使用 VPN 连接到支持的地区（美国、欧洲等）

#### 2. 凭证文件错误
**错误**: `Failed to load credentials`
**解决**: 
- 检查文件路径是否正确
- 确保文件权限可读
- 验证 JSON 格式

#### 3. 项目 ID 错误
**错误**: `This account requires setting the GOOGLE_CLOUD_PROJECT env var`
**解决**: 
- 设置正确的项目 ID
- 确保项目启用了必要的 API

#### 4. pipx 路径问题
**错误**: `pipx: command not found`
**解决**: 
- 运行 `python -m pipx ensurepath`
- 重启终端
- 手动添加到 PATH

### 调试技巧

1. **查看详细错误信息**
   ```bash
   gemini --verbose
   ```

2. **检查 MCP 服务器状态**
   - 在 Gemini CLI 中按 `Ctrl+T`

3. **验证环境变量**
   ```bash
   echo $GOOGLE_CLOUD_PROJECT
   echo $GOOGLE_APPLICATION_CREDENTIALS
   ```

## 高级配置

### 自定义启动脚本

创建 `start-analytics-mcp.ps1` (Windows) 或 `start-analytics-mcp.sh` (Linux/macOS):

**Windows PowerShell:**
```powershell
#!/usr/bin/env pwsh
# Google Analytics MCP 启动脚本

# 设置环境变量
$env:GOOGLE_CLOUD_PROJECT = "your-project-id"
$env:GEMINI_API_KEY = "your-api-key"
$env:GOOGLE_APPLICATION_CREDENTIALS = "path\to\credentials.json"

# 验证配置
Write-Host "验证配置..."
if (-not (Test-Path $env:GOOGLE_APPLICATION_CREDENTIALS)) {
    Write-Error "凭证文件不存在: $env:GOOGLE_APPLICATION_CREDENTIALS"
    exit 1
}

# 启动 Gemini CLI
Write-Host "启动 Gemini CLI..."
gemini
```

**Linux/macOS Bash:**
```bash
#!/bin/bash
# Google Analytics MCP 启动脚本

# 设置环境变量
export GOOGLE_CLOUD_PROJECT="your-project-id"
export GEMINI_API_KEY="your-api-key"
export GOOGLE_APPLICATION_CREDENTIALS="path/to/credentials.json"

# 验证配置
echo "验证配置..."
if [ ! -f "$GOOGLE_APPLICATION_CREDENTIALS" ]; then
    echo "错误: 凭证文件不存在: $GOOGLE_APPLICATION_CREDENTIALS"
    exit 1
fi

# 启动 Gemini CLI
echo "启动 Gemini CLI..."
gemini
```

### 多项目配置

如果需要管理多个 Google Analytics 项目，可以创建多个配置：

```json
{
  "selectedAuthType": "gemini-api-key",
  "mcpServers": {
    "analytics-mcp-project1": {
      "command": "python",
      "args": [...],
      "env": {
        "GOOGLE_APPLICATION_CREDENTIALS": "/path/to/project1-creds.json",
        "GOOGLE_CLOUD_PROJECT": "project1-id"
      }
    },
    "analytics-mcp-project2": {
      "command": "python",
      "args": [...],
      "env": {
        "GOOGLE_APPLICATION_CREDENTIALS": "/path/to/project2-creds.json",
        "GOOGLE_CLOUD_PROJECT": "project2-id"
      }
    }
  }
}
```

## 安全最佳实践

1. **凭证文件安全**
   - 设置适当的文件权限 (600)
   - 不要提交到版本控制
   - 定期轮换凭证

2. **API Key 管理**
   - 使用环境变量存储
   - 限制 API Key 权限
   - 监控使用情况

3. **网络安全**
   - 使用 HTTPS 连接
   - 考虑使用 VPN
   - 监控异常访问

## 更新和维护

### 更新 Gemini CLI
```bash
npm update -g @google/gemini-cli
```

### 更新 MCP 服务器
MCP 服务器会在每次运行时自动从 GitHub 获取最新版本。

### 清理缓存
```bash
# 清理 pipx 缓存
python -m pipx uninstall-all
```

## 支持和资源

- **官方文档**: [Google Analytics MCP GitHub](https://github.com/googleanalytics/google-analytics-mcp)
- **Gemini CLI 文档**: [Gemini CLI GitHub](https://github.com/google-gemini/gemini-cli)
- **Google Analytics API**: [官方文档](https://developers.google.com/analytics)

## 总结

完成以上步骤后，您将拥有一个完全配置的 Google Analytics MCP 环境，能够：

- 通过自然语言查询 Google Analytics 数据
- 生成自动化报告
- 进行数据分析和可视化
- 与其他 MCP 服务器集成

安装过程虽然涉及多个步骤，但每个步骤都是必要的，确保了系统的稳定性和安全性。

## 附录

### A. 完整安装命令清单

**Windows PowerShell 一键安装脚本:**
```powershell
# 安装 Gemini CLI
npm install -g @google/gemini-cli

# 安装 pipx
python -m pip install --user pipx
python -m pipx ensurepath

# 创建配置目录
mkdir $env:USERPROFILE\.gemini

# 设置环境变量（替换为您的实际值）
$env:GOOGLE_CLOUD_PROJECT = "your-project-id"
$env:GEMINI_API_KEY = "your-gemini-api-key"
$env:GOOGLE_APPLICATION_CREDENTIALS = "path\to\your\credentials.json"

# 创建配置文件（需要手动编辑）
Write-Host "请编辑 $env:USERPROFILE\.gemini\settings.json 配置文件"

# 启动 Gemini CLI
gemini
```

**Linux/macOS 一键安装脚本:**
```bash
#!/bin/bash
# 安装 Gemini CLI
npm install -g @google/gemini-cli

# 安装 pipx
python3 -m pip install --user pipx
python3 -m pipx ensurepath

# 创建配置目录
mkdir -p ~/.gemini

# 设置环境变量（替换为您的实际值）
export GOOGLE_CLOUD_PROJECT="your-project-id"
export GEMINI_API_KEY="your-gemini-api-key"
export GOOGLE_APPLICATION_CREDENTIALS="path/to/your/credentials.json"

# 创建配置文件（需要手动编辑）
echo "请编辑 ~/.gemini/settings.json 配置文件"

# 启动 Gemini CLI
gemini
```

### B. 配置文件模板

**完整的 settings.json 模板:**
```json
{
  "selectedAuthType": "gemini-api-key",
  "mcpServers": {
    "analytics-mcp": {
      "command": "python",
      "args": [
        "-m",
        "pipx",
        "run",
        "--spec",
        "git+https://github.com/googleanalytics/google-analytics-mcp.git",
        "google-analytics-mcp"
      ],
      "env": {
        "GOOGLE_APPLICATION_CREDENTIALS": "REPLACE_WITH_YOUR_CREDENTIALS_PATH"
      }
    }
  },
  "hasSeenIdeIntegrationNudge": true
}
```

### C. 环境变量配置文件

**Windows .env 文件示例:**
```env
GOOGLE_CLOUD_PROJECT=your-project-id
GEMINI_API_KEY=your-gemini-api-key
GOOGLE_APPLICATION_CREDENTIALS=C:\path\to\your\credentials.json
```

**Linux/macOS .env 文件示例:**
```env
GOOGLE_CLOUD_PROJECT=your-project-id
GEMINI_API_KEY=your-gemini-api-key
GOOGLE_APPLICATION_CREDENTIALS=/path/to/your/credentials.json
```

### D. 验证检查清单

安装完成后，请确认以下项目：

- [ ] Node.js 版本 >= 14
- [ ] Python 版本 >= 3.8
- [ ] Gemini CLI 已安装并可运行
- [ ] pipx 已安装并在 PATH 中
- [ ] Google Cloud 项目已创建
- [ ] Analytics API 已启用
- [ ] OAuth 凭证文件已下载
- [ ] 环境变量已正确设置
- [ ] settings.json 配置文件已创建
- [ ] Gemini CLI 可以成功启动
- [ ] MCP 服务器显示已连接

### E. 常用命令参考

**检查安装状态:**
```bash
# 检查版本
node --version
python --version
gemini --version

# 检查环境变量
echo $GOOGLE_CLOUD_PROJECT
echo $GEMINI_API_KEY
echo $GOOGLE_APPLICATION_CREDENTIALS

# 验证配置文件
python -m json.tool ~/.gemini/settings.json
```

**重置配置:**
```bash
# 删除配置文件
rm ~/.gemini/settings.json

# 清理 pipx 缓存
python -m pipx uninstall-all

# 重新创建配置
mkdir -p ~/.gemini
```

**调试命令:**
```bash
# 详细模式启动
gemini --verbose

# 手动测试 MCP 服务器
python -m pipx run --spec git+https://github.com/googleanalytics/google-analytics-mcp.git google-analytics-mcp --help
```

### F. 项目结构示例

推荐的项目目录结构：
```
google-analytics-mcp/
├── docs/
│   ├── google-analytics-mcp-installation-guide.md
│   └── environment-variables-setup.md
├── credentials/
│   └── client_secret_xxx.json
├── scripts/
│   ├── setup-env.ps1
│   └── setup-env.sh
└── README.md
```

### G. 相关链接

- [Google Analytics MCP GitHub Repository](https://github.com/googleanalytics/google-analytics-mcp)
- [Gemini CLI Documentation](https://github.com/google-gemini/gemini-cli)
- [Google Cloud Console](https://console.cloud.google.com/)
- [Google Analytics API Documentation](https://developers.google.com/analytics)
- [Model Context Protocol Specification](https://modelcontextprotocol.io/)

---

**文档版本**: 1.0
**最后更新**: 2025-01-20
**适用版本**: Gemini CLI 0.1.22+, Google Analytics MCP latest
