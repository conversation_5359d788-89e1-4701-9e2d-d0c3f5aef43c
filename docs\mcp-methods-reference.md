# Google Analytics MCP 方法和功能参考

## 🎯 概述

Google Analytics MCP服务器提供了6个核心工具，涵盖账户管理、数据报告和实时监控等功能。所有工具都通过MCP协议暴露给AI客户端，支持自然语言查询。

## 📊 工具分类

### 🏢 账户和属性管理 (Admin API)
- `get_account_summaries` - 获取账户和属性概览
- `get_property_details` - 获取属性详细信息
- `list_google_ads_links` - 查看Google Ads链接

### 📈 数据报告 (Data API)
- `run_report` - 生成标准数据报告
- `get_custom_dimensions_and_metrics` - 获取自定义维度和指标

### ⚡ 实时监控 (Realtime API)
- `run_realtime_report` - 获取实时数据报告

## 🔧 详细方法参考

### 1. get_account_summaries

**功能描述**：获取用户的Google Analytics账户和属性信息

**参数**：无

**返回数据**：
```json
[
  {
    "name": "accountSummaries/*********",
    "account": "accounts/*********", 
    "display_name": "我的网站",
    "property_summaries": [
      {
        "property": "properties/*********",
        "display_name": "www.example.com",
        "property_type": "PROPERTY_TYPE_ORDINARY"
      }
    ]
  }
]
```

**使用场景**：
- 查看所有可访问的Analytics账户
- 获取属性ID用于后续查询
- 了解账户结构和权限

**AI查询示例**：
```
"显示我的Google Analytics账户"
"我有哪些网站属性？"
"列出所有可用的Analytics属性"
```

### 2. get_property_details

**功能描述**：获取指定属性的详细信息

**参数**：
- `property_id` (int|str): 属性ID，支持数字或"properties/123456"格式

**返回数据**：
```json
{
  "name": "properties/*********",
  "display_name": "www.example.com",
  "property_type": "PROPERTY_TYPE_ORDINARY",
  "create_time": "2023-01-01T00:00:00Z",
  "update_time": "2024-01-01T00:00:00Z",
  "time_zone": "Asia/Shanghai",
  "currency_code": "USD"
}
```

**使用场景**：
- 查看属性配置信息
- 确认时区和货币设置
- 获取属性创建和更新时间

**AI查询示例**：
```
"显示属性*********的详细信息"
"这个网站的时区设置是什么？"
"属性配置信息"
```

### 3. list_google_ads_links

**功能描述**：获取属性关联的Google Ads账户链接

**参数**：
- `property_id` (int|str): 属性ID

**返回数据**：
```json
[
  {
    "name": "properties/*********/googleAdsLinks/12345",
    "customer_id": "123-456-7890",
    "ads_personalization_enabled": true,
    "create_time": "2023-01-01T00:00:00Z"
  }
]
```

**使用场景**：
- 查看Google Ads集成状态
- 确认广告个性化设置
- 管理广告账户关联

**AI查询示例**：
```
"这个属性关联了哪些Google Ads账户？"
"广告个性化是否启用？"
"显示Google Ads链接状态"
```

### 4. run_report

**功能描述**：生成标准Google Analytics数据报告

**参数**：
- `property_id` (int|str): 属性ID
- `date_ranges` (List[Dict]): 日期范围
- `dimensions` (List[str]): 维度列表
- `metrics` (List[str]): 指标列表
- `dimension_filter` (Dict, 可选): 维度过滤器
- `metric_filter` (Dict, 可选): 指标过滤器
- `order_bys` (List[Dict], 可选): 排序规则
- `limit` (int, 可选): 结果数量限制
- `offset` (int, 可选): 结果偏移量
- `currency_code` (str, 可选): 货币代码

**常用维度**：
- `date` - 日期
- `country` - 国家
- `city` - 城市
- `deviceCategory` - 设备类型
- `browser` - 浏览器
- `operatingSystem` - 操作系统
- `sourceMedium` - 流量来源/媒介
- `pagePath` - 页面路径
- `eventName` - 事件名称

**常用指标**：
- `activeUsers` - 活跃用户数
- `sessions` - 会话数
- `screenPageViews` - 页面浏览量
- `bounceRate` - 跳出率
- `averageSessionDuration` - 平均会话时长
- `conversions` - 转化次数
- `totalRevenue` - 总收入

**返回数据**：
```json
{
  "dimension_headers": [{"name": "date"}],
  "metric_headers": [
    {"name": "activeUsers", "type_": "TYPE_INTEGER"},
    {"name": "sessions", "type_": "TYPE_INTEGER"}
  ],
  "rows": [
    {
      "dimension_values": [{"value": "20250819"}],
      "metric_values": [{"value": "3"}, {"value": "6"}]
    }
  ],
  "row_count": 1
}
```

**使用场景**：
- 生成各种数据报告
- 分析用户行为和流量
- 监控关键业务指标
- 进行多维度数据分析

**AI查询示例**：
```
"昨天有多少用户访问了网站？"
"过去7天的流量趋势如何？"
"哪些页面最受欢迎？"
"不同设备的用户分布情况？"
"主要流量来源是什么？"
```

### 5. get_custom_dimensions_and_metrics

**功能描述**：获取属性的自定义维度和指标

**参数**：
- `property_id` (int|str): 属性ID

**返回数据**：
```json
{
  "custom_dimensions": [
    {
      "api_name": "customUser:user_type",
      "display_name": "用户类型",
      "description": "区分新用户和老用户",
      "scope": "USER"
    }
  ],
  "custom_metrics": [
    {
      "api_name": "customEvent:engagement_score", 
      "display_name": "参与度分数",
      "description": "用户参与度评分",
      "measurement_unit": "STANDARD"
    }
  ]
}
```

**使用场景**：
- 查看可用的自定义维度和指标
- 了解业务特定的数据字段
- 为报告查询准备参数

**AI查询示例**：
```
"这个属性有哪些自定义维度？"
"显示所有自定义指标"
"可用的业务特定数据字段"
```

### 6. run_realtime_report

**功能描述**：获取实时Google Analytics数据

**参数**：
- `property_id` (int|str): 属性ID
- `dimensions` (List[str]): 实时维度列表
- `metrics` (List[str]): 实时指标列表
- `dimension_filter` (Dict, 可选): 维度过滤器
- `metric_filter` (Dict, 可选): 指标过滤器
- `order_bys` (List[Dict], 可选): 排序规则
- `limit` (int, 可选): 结果数量限制
- `offset` (int, 可选): 结果偏移量

**实时维度**：
- `country` - 国家
- `region` - 地区
- `city` - 城市
- `deviceCategory` - 设备类型
- `operatingSystem` - 操作系统
- `browser` - 浏览器
- `eventName` - 事件名称
- `unifiedScreenName` - 屏幕名称

**实时指标**：
- `activeUsers` - 当前活跃用户
- `screenPageViews` - 实时页面浏览
- `eventCount` - 事件计数
- `conversions` - 实时转化

**返回数据**：
```json
{
  "dimension_headers": [{"name": "country"}],
  "metric_headers": [{"name": "activeUsers", "type_": "TYPE_INTEGER"}],
  "rows": [
    {
      "dimension_values": [{"value": "China"}],
      "metric_values": [{"value": "5"}]
    }
  ],
  "row_count": 1
}
```

**使用场景**：
- 监控当前网站活动
- 实时用户行为分析
- 即时营销活动效果监控
- 实时异常检测

**AI查询示例**：
```
"现在有多少用户在线？"
"实时访问者来自哪些国家？"
"当前最活跃的页面是什么？"
"实时转化情况如何？"
```

## 🎯 使用最佳实践

### 1. 参数格式规范

**日期范围格式**：
```json
[
  {"start_date": "yesterday", "end_date": "today", "name": "昨天到今天"},
  {"start_date": "7daysAgo", "end_date": "yesterday", "name": "过去7天"},
  {"start_date": "2025-01-01", "end_date": "2025-01-31", "name": "一月份"}
]
```

**过滤器格式**：
```json
{
  "filter": {
    "field_name": "country",
    "string_filter": {
      "match_type": 1,
      "value": "China",
      "case_sensitive": false
    }
  }
}
```

### 2. 常用查询模式

**基础用户统计**：
```python
run_report(
    property_id="*********",
    date_ranges=[{"start_date": "yesterday", "end_date": "yesterday"}],
    dimensions=["date"],
    metrics=["activeUsers", "sessions", "screenPageViews"]
)
```

**流量来源分析**：
```python
run_report(
    property_id="*********", 
    date_ranges=[{"start_date": "7daysAgo", "end_date": "yesterday"}],
    dimensions=["sourceMedium"],
    metrics=["sessions", "activeUsers"],
    order_bys=[{"metric": {"metric_name": "sessions"}, "desc": true}]
)
```

**实时监控**：
```python
run_realtime_report(
    property_id="*********",
    dimensions=["country", "deviceCategory"],
    metrics=["activeUsers"]
)
```

## 🔍 API限制和注意事项

### 配额限制
- **标准报告**: 每天25,000次请求
- **实时报告**: 每小时10,000次请求
- **并发请求**: 最多10个同时请求

### 数据限制
- **维度组合**: 最多9个维度
- **指标数量**: 最多10个指标
- **结果行数**: 最多250,000行
- **实时数据**: 仅支持特定维度和指标

### 权限要求
- **只读权限**: `https://www.googleapis.com/auth/analytics.readonly`
- **属性访问**: 需要对应属性的查看权限
- **实时数据**: 可能需要额外的实时数据权限

## 🎉 总结

Google Analytics MCP提供了完整的数据访问能力，通过6个核心工具覆盖了：
- 📊 **账户管理** - 查看和管理Analytics账户结构
- 📈 **数据报告** - 生成各种维度的数据分析报告  
- ⚡ **实时监控** - 获取当前网站活动的实时数据
- 🔧 **自定义分析** - 支持自定义维度和指标查询

配置完成后，AI助手可以自动调用这些工具，为用户提供智能的数据分析服务！
