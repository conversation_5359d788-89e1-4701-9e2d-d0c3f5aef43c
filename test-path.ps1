# Test PATH and google-analytics-mcp command

Write-Host "Testing PATH and google-analytics-mcp command..." -ForegroundColor Green

# Check if the binary exists
$BinaryPath = "C:\Users\<USER>\AppData\Local\GoogleAnalyticsMCP\bin\google-analytics-mcp.exe"
if (Test-Path $BinaryPath) {
    Write-Host "✅ Binary exists: $BinaryPath" -ForegroundColor Green
} else {
    Write-Host "❌ Binary not found: $BinaryPath" -ForegroundColor Red
    exit 1
}

# Check PATH
$UserPath = [Environment]::GetEnvironmentVariable("PATH", "User")
$BinDir = "C:\Users\<USER>\AppData\Local\GoogleAnalyticsMCP\bin"

if ($UserPath -like "*$BinDir*") {
    Write-Host "✅ PATH contains: $BinDir" -ForegroundColor Green
} else {
    Write-Host "❌ PATH does not contain: $BinDir" -ForegroundColor Red
    Write-Host "Current PATH: $UserPath" -ForegroundColor Yellow
}

# Test command resolution
try {
    $Command = Get-Command "google-analytics-mcp" -ErrorAction Stop
    Write-Host "✅ Command resolved: $($Command.Source)" -ForegroundColor Green
} catch {
    Write-Host "❌ Command not found in PATH" -ForegroundColor Red
    Write-Host "You may need to restart your terminal or run:" -ForegroundColor Yellow
    Write-Host "  $BinaryPath" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "To use the command globally, you may need to:" -ForegroundColor Yellow
Write-Host "1. Restart your terminal/PowerShell" -ForegroundColor White
Write-Host "2. Or run the full path: $BinaryPath" -ForegroundColor White
