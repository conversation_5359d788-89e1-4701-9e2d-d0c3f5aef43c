# MCP Inspector 完整使用指南

## 🎉 测试成功确认

经过完整测试，Google Analytics MCP Inspector 已成功启动并正常工作：

```
🚀 MCP Inspector is up and running at:
   http://localhost:6274/?MCP_PROXY_AUTH_TOKEN=bfc98620dbd03a22b98f8e3a32672c37aa92bb3845e059a66bb3aacd0f65c3bb
🌐 Opening browser...
```

---

## 🌐 什么是 MCP Inspector？

MCP Inspector 是一个强大的 Web 界面工具，专门用于：

### 核心功能
- 📋 **可视化工具列表**：显示所有可用的 MCP 工具
- 🧪 **实时测试功能**：直接在浏览器中测试 API 调用
- 📊 **详细响应查看**：查看完整的 JSON 请求和响应
- 🔍 **调试和诊断**：帮助排查 MCP 服务器问题
- 📝 **代码示例生成**：提供可复制的 API 调用格式

### 技术架构
- **代理服务器**：localhost:6277 (处理 MCP 通信)
- **Web 界面**：localhost:6274 (用户交互界面)
- **认证机制**：基于令牌的安全访问
- **实时通信**：WebSocket 连接确保实时响应

---

## 🚀 启动 MCP Inspector

### 方法1：手动启动
```bash
# 设置环境变量
$env:GOOGLE_APPLICATION_CREDENTIALS="C:\Users\<USER>\AppData\Roaming\gcloud\application_default_credentials.json"
$env:GOOGLE_CLOUD_PROJECT="sound-silicon-469508-a8"

# 启动 Inspector
mcp dev official-ga-mcp/analytics_mcp/server.py
```

### 方法2：使用批处理脚本
```bash
# 运行自动化脚本
start-mcp-inspector.bat
```

### 启动成功标志
看到以下输出表示启动成功：
```
⚙️ Proxy server listening on localhost:6277
🔑 Session token: [长字符串令牌]
🚀 MCP Inspector is up and running at: http://localhost:6274/...
🌐 Opening browser...
```

---

## 🖥️ Inspector 界面详解

### 左侧面板 - 工具列表
显示所有可用的 Google Analytics MCP 工具：

**管理类工具**
- `get_account_summaries_analytics` - 获取账户摘要
- `get_property_details_analytics` - 获取属性详情
- `get_custom_dimensions_and_metrics_analytics` - 获取自定义维度和指标
- `list_google_ads_links_analytics` - 列出 Google Ads 链接

**报告类工具**
- `run_report_analytics` - 运行标准数据报告
- `run_realtime_report_analytics` - 运行实时数据报告

### 右侧面板 - 测试区域
- **参数输入框**：输入 JSON 格式的工具参数
- **Call Tool 按钮**：执行工具调用
- **响应显示区**：显示返回的 JSON 数据
- **状态指示器**：绿色表示成功，红色表示错误

---

## 🧪 实际操作示例

### 示例1：获取账户摘要
1. **选择工具**：点击左侧的 `get_account_summaries_analytics`
2. **输入参数**：此工具无需参数，直接点击 "Call Tool"
3. **查看结果**：右侧显示所有 Google Analytics 账户信息

**预期结果**：
```json
{
  "accounts": [
    {
      "name": "accounts/*********",
      "display_name": "kickstarter",
      "property_summaries": [...]
    }
  ]
}
```

### 示例2：运行数据报告
1. **选择工具**：点击 `run_report_analytics`
2. **输入参数**：
```json
{
  "property_id": "*********",
  "date_ranges": [{"start_date": "30daysAgo", "end_date": "yesterday"}],
  "dimensions": ["country", "deviceCategory"],
  "metrics": ["sessions", "totalUsers", "screenPageViews"]
}
```
3. **执行调用**：点击 "Call Tool"
4. **分析结果**：查看返回的详细报告数据

### 示例3：实时数据查询
1. **选择工具**：点击 `run_realtime_report_analytics`
2. **输入参数**：
```json
{
  "property_id": "*********",
  "dimensions": ["country", "deviceCategory"],
  "metrics": ["activeUsers"]
}
```
3. **查看实时数据**：获取当前在线用户信息

---

## 🛠️ 故障排除指南

### 问题1：端口被占用
**错误现象**：
```
❌  Proxy Server PORT IS IN USE at port 6277 ❌
❌  MCP Inspector PORT IS IN USE at http://localhost:6274 ❌
```

**解决步骤**：
```bash
# 1. 查找占用端口的进程
netstat -ano | findstr :6277
netstat -ano | findstr :6274

# 2. 终止进程（替换 <PID> 为实际进程ID）
taskkill /PID <PID> /F

# 3. 重新启动 Inspector
mcp dev official-ga-mcp/analytics_mcp/server.py
```

### 问题2：认证失败
**错误现象**：工具调用返回认证错误

**解决方案**：
```bash
# 检查环境变量
echo $env:GOOGLE_APPLICATION_CREDENTIALS
echo $env:GOOGLE_CLOUD_PROJECT

# 重新设置环境变量
$env:GOOGLE_APPLICATION_CREDENTIALS="C:\Users\<USER>\AppData\Roaming\gcloud\application_default_credentials.json"
$env:GOOGLE_CLOUD_PROJECT="sound-silicon-469508-a8"

# 验证认证
gcloud auth application-default print-access-token
```

### 问题3：工具调用失败
**错误现象**：点击 "Call Tool" 后显示错误

**调试步骤**：
1. 检查参数格式是否为有效 JSON
2. 验证必需参数是否都已提供
3. 确认 property_id 等值是否正确
4. 查看详细错误信息进行针对性修复

---

## 💡 最佳实践

### 1. 参数验证
- 使用 JSON 验证工具确保参数格式正确
- 参考 Google Analytics API 文档了解参数要求
- 从简单参数开始，逐步增加复杂度

### 2. 错误处理
- 仔细阅读错误信息，通常包含具体的问题描述
- 检查 API 配额是否已用完
- 验证账户权限是否足够

### 3. 性能优化
- 避免频繁调用相同的 API
- 合理设置日期范围，避免查询过大数据集
- 使用适当的维度和指标组合

---

## 🎯 从 Inspector 到 Gemini CLI

### 1. 测试验证流程
1. **在 Inspector 中测试**：确保工具和参数正确
2. **复制成功的调用**：记录有效的参数格式
3. **转换为 Gemini CLI**：将测试结果应用到实际使用

### 2. 参数转换示例
**Inspector 中的调用**：
```json
{
  "property_id": "*********",
  "date_ranges": [{"start_date": "30daysAgo", "end_date": "yesterday"}],
  "dimensions": ["country"],
  "metrics": ["sessions"]
}
```

**Gemini CLI 中的使用**：
```bash
gemini "使用 Google Analytics MCP 分析属性 ********* 最近30天按国家分组的会话数据"
```

---

## 📊 成功验证清单

- [x] MCP Inspector 成功启动
- [x] Web 界面可正常访问 (http://localhost:6274/)
- [x] 所有 Google Analytics 工具都显示在左侧面板
- [x] 可以成功调用 `get_account_summaries_analytics`
- [x] 可以运行数据报告并获取结果
- [x] 认证和权限配置正确
- [x] 端口冲突问题已解决

---

## 🎉 结论

MCP Inspector 现在完全可用，为 Gemini CLI 集成 Google Analytics MCP 提供了完美的测试和调试环境。您可以：

1. **充分测试所有 MCP 工具**
2. **验证参数和响应格式**
3. **调试任何配置问题**
4. **为 Gemini CLI 集成做好准备**

所有组件都已验证正常工作，您现在可以放心地在 Gemini CLI 中使用 Google Analytics MCP 了！
