@echo off
title Google Analytics MCP Authentication Helper

echo.
echo ==========================================
echo   Google Analytics MCP Authentication Helper
echo ==========================================
echo.

:: Activate virtual environment
if exist ".venv\Scripts\activate.bat" (
    call .venv\Scripts\activate.bat
    echo OK: Virtual environment activated
) else (
    echo WARNING: Using system Python environment
)

echo.
echo Authentication Steps:
echo.
echo 1. Script will generate Google authentication URL
echo 2. <PERSON><PERSON><PERSON> will automatically open authentication page
echo 3. Login to your Google account in browser
echo 4. Authorize application to access Google Analytics
echo 5. Copy authorization code from browser
echo 6. Return to this window and paste authorization code
echo 7. Wait for authentication completion and testing
echo.

echo Required Permission Scopes:
echo    - Google Analytics read-only access
echo    - Google Cloud Platform access
echo    - Analytics user management access
echo.

set /p ready="Ready to start authentication? (y/N): "
if /i not "%ready%"=="y" (
    echo Authentication cancelled
    pause
    exit /b 0
)

echo.
echo Starting authentication process...
echo.
echo Please wait for authentication URL generation...
echo Browser will automatically open authentication page
echo If browser does not open automatically, please manually copy URL to browser
echo.
echo ========================================

:: Run authentication script
python complete_reauth.py

echo ========================================
echo.

:: Check authentication result
if errorlevel 1 (
    echo ERROR: Error occurred during authentication process
    echo.
    echo Possible solutions:
    echo    1. Check network connection
    echo    2. Confirm Google account permissions
    echo    3. Re-copy complete authorization code
    echo    4. Try using different browser
    echo.
) else (
    echo OK: Authentication process completed
    echo.
    echo Performing final verification test...
    
    :: Quick verification test
    python test_mcp_simple.py
)

echo.
echo Usage Instructions:
echo    Now you can directly ask AI:
echo    - "How many users visited the website yesterday?"
echo    - "What is the traffic trend for the past 7 days?"
echo    - "What are the main traffic sources?"
echo    - "How many users are currently online?"
echo.
echo Documentation: docs\google-analytics-mcp-authentication-guide.md
echo.

pause
