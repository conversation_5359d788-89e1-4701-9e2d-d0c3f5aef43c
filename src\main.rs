use anyhow::{Result, Context};
use std::env;
use std::path::PathBuf;
use std::fs;
use tokio::process::Command as AsyncCommand;
use serde::{Deserialize, Serialize};

#[tokio::main]
async fn main() -> Result<()> {
    let args: Vec<String> = env::args().collect();

    // 检查命令行参数
    if args.len() > 1 {
        match args[1].as_str() {
            "--config" | "-c" => {
                run_config_wizard().await?;
                return Ok(());
            }
            "--help" | "-h" => {
                print_help();
                return Ok(());
            }
            _ => {
                eprintln!("未知参数: {}", args[1]);
                print_help();
                return Ok(());
            }
        }
    }

    // 设置环境变量
    setup_environment()?;

    // 启动Google Analytics MCP服务器
    run_mcp_server().await
}

/// 设置必要的环境变量
fn setup_environment() -> Result<()> {
    // 设置Google Cloud认证文件路径
    if env::var("GOOGLE_APPLICATION_CREDENTIALS").is_err() {
        // 尝试多个可能的认证文件位置
        let possible_paths = get_possible_credential_paths()?;

        let mut found_credentials = false;
        for path in possible_paths {
            if path.exists() {
                env::set_var("GOOGLE_APPLICATION_CREDENTIALS", &path);
                println!("✅ 设置认证文件路径: {:?}", path);
                found_credentials = true;
                break;
            }
        }

        if !found_credentials {
            eprintln!("⚠️  警告: 未找到Google Cloud认证文件");
            eprintln!("   请运行: gcloud auth application-default login");
            eprintln!("   或设置环境变量: GOOGLE_APPLICATION_CREDENTIALS=path/to/credentials.json");
        }
    }

    // 设置项目ID
    if env::var("GOOGLE_CLOUD_PROJECT").is_err() {
        // 尝试从配置文件读取项目ID
        if let Ok(project_id) = load_project_id_from_config() {
            env::set_var("GOOGLE_CLOUD_PROJECT", &project_id);
            println!("✅ 从配置文件设置项目ID: {}", project_id);
        } else {
            eprintln!("⚠️  警告: 未设置Google Cloud项目ID");
            eprintln!("   请设置环境变量: GOOGLE_CLOUD_PROJECT=your-project-id");
            eprintln!("   或在配置文件中设置项目ID");
        }
    }

    Ok(())
}

/// 启动MCP服务器
async fn run_mcp_server() -> Result<()> {
    println!("🚀 启动 Google Analytics MCP 服务器...");
    
    // 获取当前可执行文件的目录
    let exe_path = env::current_exe()
        .context("无法获取当前可执行文件路径")?;
    let exe_dir = exe_path.parent()
        .context("无法获取可执行文件目录")?;
    
    // 查找Python脚本路径
    let script_paths = vec![
        // 相对于exe的路径
        exe_dir.join("official-ga-mcp").join("analytics_mcp").join("server.py"),
        // 当前工作目录的路径
        PathBuf::from("official-ga-mcp").join("analytics_mcp").join("server.py"),
        // 绝对路径（开发环境）
        PathBuf::from(r"d:\CLT\DM\google-analytics-mcp_gitee\official-ga-mcp\analytics_mcp\server.py"),
    ];
    
    let mut script_path = None;
    for path in script_paths {
        if path.exists() {
            script_path = Some(path);
            break;
        }
    }
    
    let script_path = script_path
        .context("未找到 Google Analytics MCP 服务器脚本")?;
    
    println!("📄 使用脚本: {:?}", script_path);
    
    // 检查Python是否可用
    let python_cmd = find_python_command().await?;
    println!("🐍 使用Python: {}", python_cmd);
    
    // 启动Python MCP服务器
    let mut child = AsyncCommand::new(&python_cmd)
        .arg(&script_path)
        .spawn()
        .context("启动Python MCP服务器失败")?;
    
    println!("✅ Google Analytics MCP 服务器已启动");
    println!("📡 等待MCP客户端连接...");
    
    // 等待子进程结束
    let status = child.wait().await
        .context("等待MCP服务器进程失败")?;
    
    if status.success() {
        println!("✅ MCP服务器正常退出");
    } else {
        eprintln!("❌ MCP服务器异常退出，状态码: {:?}", status.code());
    }
    
    Ok(())
}

/// 查找可用的Python命令
async fn find_python_command() -> Result<String> {
    let python_commands = vec!["python", "python3", "py"];
    
    for cmd in python_commands {
        if let Ok(output) = AsyncCommand::new(cmd)
            .arg("--version")
            .output()
            .await
        {
            if output.status.success() {
                return Ok(cmd.to_string());
            }
        }
    }
    
    Err(anyhow::anyhow!("未找到可用的Python命令，请确保Python已安装并在PATH中"))
}

/// 配置文件结构
#[derive(Serialize, Deserialize, Default)]
struct Config {
    pub google_cloud_project: Option<String>,
    pub credentials_path: Option<String>,
}

/// 获取可能的认证文件路径
fn get_possible_credential_paths() -> Result<Vec<PathBuf>> {
    let mut paths = Vec::new();

    if let Some(home_dir) = dirs::home_dir() {
        // Windows默认路径
        paths.push(home_dir.join("AppData").join("Roaming").join("gcloud").join("application_default_credentials.json"));

        // Linux/Mac默认路径
        paths.push(home_dir.join(".config").join("gcloud").join("application_default_credentials.json"));

        // 用户自定义路径（从配置文件读取）
        if let Ok(config) = load_config() {
            if let Some(custom_path) = config.credentials_path {
                paths.push(PathBuf::from(custom_path));
            }
        }
    }

    Ok(paths)
}

/// 从配置文件加载项目ID
fn load_project_id_from_config() -> Result<String> {
    let config = load_config()?;
    config.google_cloud_project
        .ok_or_else(|| anyhow::anyhow!("配置文件中未找到项目ID"))
}

/// 加载配置文件
fn load_config() -> Result<Config> {
    let config_path = get_config_path()?;

    if !config_path.exists() {
        // 如果配置文件不存在，创建默认配置
        create_default_config(&config_path)?;
        return Ok(Config::default());
    }

    let config_content = fs::read_to_string(&config_path)
        .context("读取配置文件失败")?;

    let config: Config = serde_json::from_str(&config_content)
        .context("解析配置文件失败")?;

    Ok(config)
}

/// 获取配置文件路径
fn get_config_path() -> Result<PathBuf> {
    let exe_path = env::current_exe()
        .context("无法获取当前可执行文件路径")?;
    let exe_dir = exe_path.parent()
        .context("无法获取可执行文件目录")?;

    Ok(exe_dir.join("config.json"))
}

/// 创建默认配置文件
fn create_default_config(config_path: &PathBuf) -> Result<()> {
    let default_config = Config {
        google_cloud_project: None,
        credentials_path: None,
    };

    let config_json = serde_json::to_string_pretty(&default_config)
        .context("序列化默认配置失败")?;

    fs::write(config_path, config_json)
        .context("写入默认配置文件失败")?;

    println!("✅ 创建默认配置文件: {:?}", config_path);
    println!("💡 请编辑配置文件设置您的Google Cloud项目ID");

    Ok(())
}

/// 打印帮助信息
fn print_help() {
    println!("Google Analytics MCP Server");
    println!();
    println!("用法:");
    println!("  ga                    启动MCP服务器");
    println!("  ga --config, -c       运行配置向导");
    println!("  ga --help, -h         显示此帮助信息");
    println!();
    println!("配置文件位置: config.json (与可执行文件同目录)");
    println!();
    println!("环境变量:");
    println!("  GOOGLE_APPLICATION_CREDENTIALS  Google Cloud认证文件路径");
    println!("  GOOGLE_CLOUD_PROJECT            Google Cloud项目ID");
}

/// 运行配置向导
async fn run_config_wizard() -> Result<()> {
    println!("🔧 Google Analytics MCP 配置向导");
    println!("==================================");
    println!();

    let config_path = get_config_path()?;
    let mut config = load_config().unwrap_or_default();

    // 配置项目ID
    println!("📋 步骤 1: 设置Google Cloud项目ID");
    if let Some(current_project) = &config.google_cloud_project {
        println!("当前项目ID: {}", current_project);
    }

    println!("请输入您的Google Cloud项目ID (留空保持当前设置):");
    print!("> ");
    use std::io::{self, Write};
    io::stdout().flush()?;

    let mut input = String::new();
    io::stdin().read_line(&mut input)?;
    let input = input.trim();

    if !input.is_empty() {
        config.google_cloud_project = Some(input.to_string());
        println!("✅ 项目ID已设置为: {}", input);
    }

    println!();

    // 配置认证文件路径
    println!("🔑 步骤 2: 设置认证文件路径");
    println!("通常情况下，运行 'gcloud auth application-default login' 后会自动找到认证文件。");
    println!("如果需要自定义路径，请输入完整路径 (留空使用默认查找):");

    if let Some(current_creds) = &config.credentials_path {
        println!("当前自定义路径: {}", current_creds);
    }

    print!("> ");
    io::stdout().flush()?;

    let mut input = String::new();
    io::stdin().read_line(&mut input)?;
    let input = input.trim();

    if !input.is_empty() {
        config.credentials_path = Some(input.to_string());
        println!("✅ 认证文件路径已设置为: {}", input);
    } else if config.credentials_path.is_some() {
        println!("保持当前自定义路径设置");
    } else {
        println!("✅ 将使用默认路径查找");
    }

    println!();

    // 保存配置
    let config_json = serde_json::to_string_pretty(&config)
        .context("序列化配置失败")?;

    fs::write(&config_path, config_json)
        .context("保存配置文件失败")?;

    println!("✅ 配置已保存到: {:?}", config_path);
    println!();
    println!("🚀 现在您可以运行 'ga' 启动MCP服务器");

    Ok(())
}
