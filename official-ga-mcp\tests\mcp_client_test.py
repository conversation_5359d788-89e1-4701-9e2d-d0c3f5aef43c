import asyncio
import json
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

async def test_mcp_client():
    # MCP 服务器参数
    server_params = StdioServerParameters(
        command="google-analytics-mcp",
        args=[],
        env={
            "GOOGLE_APPLICATION_CREDENTIALS": r"C:\Users\<USER>\AppData\Roaming\gcloud\application_default_credentials.json",
            "GOOGLE_CLOUD_PROJECT": "sound-silicon-469508-a8"
        }
    )

    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            # 初始化
            await session.initialize()

            # 列出可用工具
            tools = await session.list_tools()
            print("🔧 可用的 MCP 工具:")
            for tool in tools.tools:
                print(f"   • {tool.name}: {tool.description}")

            # 调用获取账户摘要
            try:
                result = await session.call_tool("get_account_summaries_analytics", {})
                print("\n✅ 成功获取账户摘要:")
                print(json.dumps(result.content, indent=2, ensure_ascii=False))
            except Exception as e:
                print(f"❌ 调用失败: {e}")

# 运行测试
if __name__ == "__main__":
    asyncio.run(test_mcp_client())