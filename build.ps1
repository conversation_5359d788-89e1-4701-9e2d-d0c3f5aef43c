# Google Analytics MCP Build Script

param(
    [switch]$BuildOnly = $false
)

$ErrorActionPreference = "Stop"

Write-Host "Starting Google Analytics MCP build..." -ForegroundColor Green

# Check required tools
function Test-Command {
    param($Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

Write-Host "Checking required tools..." -ForegroundColor Yellow

if (-not (Test-Command "cargo")) {
    Write-Host "Error: cargo command not found" -ForegroundColor Red
    Write-Host "Please install Rust first: https://rustup.rs/" -ForegroundColor Red
    exit 1
}

if (-not (Test-Command "python")) {
    Write-Host "Error: python command not found" -ForegroundColor Red
    Write-Host "Please install Python first: https://python.org/" -ForegroundColor Red
    exit 1
}

# Check Python dependencies
Write-Host "Checking Python dependencies..." -ForegroundColor Yellow
$RequiredPackages = @("google-analytics-data", "google-auth", "google-cloud-core")

foreach ($Package in $RequiredPackages) {
    try {
        $ImportName = $Package.Replace('-', '_')
        python -c "import $ImportName" 2>$null
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Warning: Python package '$Package' not installed" -ForegroundColor Yellow
            Write-Host "  Please run: pip install $Package" -ForegroundColor Yellow
        }
    }
    catch {
        Write-Host "Cannot check Python package: $Package" -ForegroundColor Yellow
    }
}

# Build binary
Write-Host "Building binary..." -ForegroundColor Yellow
cargo build --release

# Check build result
$BinaryPath = "target\release\google-analytics-mcp.exe"
if (-not (Test-Path $BinaryPath)) {
    Write-Host "Binary build failed: $BinaryPath" -ForegroundColor Red
    exit 1
}

Write-Host "Binary build successful: $BinaryPath" -ForegroundColor Green

# Exit if build-only mode
if ($BuildOnly) {
    Write-Host ""
    Write-Host "Google Analytics MCP build completed!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Binary location: $BinaryPath" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "To install, run: .\build.ps1 (without -BuildOnly)"
    exit 0
}

# Installation part
$LocalAppData = $env:LOCALAPPDATA
$InstallDir = "$LocalAppData\GoogleAnalyticsMCP"
$BinDir = "$InstallDir\bin"

Write-Host "Creating install directory: $InstallDir" -ForegroundColor Yellow
New-Item -ItemType Directory -Path $BinDir -Force | Out-Null

# Copy binary
$MainExe = "$BinDir\google-analytics-mcp.exe"
Write-Host "Installing binary..." -ForegroundColor Yellow
Copy-Item $BinaryPath $MainExe -Force

# Copy Python scripts
$ScriptDir = "$InstallDir\official-ga-mcp"
Write-Host "Copying Python scripts..." -ForegroundColor Yellow
if (Test-Path "official-ga-mcp") {
    Copy-Item "official-ga-mcp" $ScriptDir -Recurse -Force
    Write-Host "Python scripts copied to: $ScriptDir" -ForegroundColor Green
} else {
    Write-Host "Warning: official-ga-mcp directory not found" -ForegroundColor Yellow
    Write-Host "  Please ensure you run this script from project root" -ForegroundColor Yellow
}

# Copy configuration template
Write-Host "Copying configuration template..." -ForegroundColor Yellow
if (Test-Path "config.template.json") {
    Copy-Item "config.template.json" "$BinDir\config.template.json" -Force
    Write-Host "Configuration template copied" -ForegroundColor Green
} else {
    Write-Host "Warning: config.template.json not found" -ForegroundColor Yellow
}

Write-Host "Binary installed to: $BinDir" -ForegroundColor Green

# Check PATH
$CurrentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
if ($CurrentPath -notlike "*$BinDir*") {
    Write-Host "Adding to user PATH..." -ForegroundColor Yellow
    
    try {
        $NewPath = if ($CurrentPath) { "$CurrentPath;$BinDir" } else { $BinDir }
        [Environment]::SetEnvironmentVariable("PATH", $NewPath, "User")
        Write-Host "Added to PATH: $BinDir" -ForegroundColor Green
        Write-Host "Please restart command prompt or PowerShell" -ForegroundColor Cyan
    }
    catch {
        Write-Host "Cannot add to PATH automatically, please add manually: $BinDir" -ForegroundColor Yellow
    }
} else {
    Write-Host "PATH already contains install directory" -ForegroundColor Green
}

Write-Host ""
Write-Host "Google Analytics MCP installation completed!" -ForegroundColor Green
Write-Host ""
Write-Host "Usage:" -ForegroundColor Cyan
Write-Host "  ga                      - Start MCP server" -ForegroundColor White
Write-Host "  ga --config             - Run configuration wizard" -ForegroundColor White
Write-Host "  ga --help               - Show help information" -ForegroundColor White
Write-Host ""
Write-Host "First-time setup:" -ForegroundColor Cyan
Write-Host "1. Run: ga --config      - Configure your Google Cloud project" -ForegroundColor White
Write-Host "2. Run: gcloud auth application-default login" -ForegroundColor White
Write-Host "3. Run: ga               - Start the MCP server" -ForegroundColor White
Write-Host ""
Write-Host "MCP Client Configuration:" -ForegroundColor Cyan
Write-Host "Add this to your MCP client config:" -ForegroundColor White
Write-Host ""
Write-Host '{' -ForegroundColor Gray
Write-Host '  "mcpServers": {' -ForegroundColor Gray
Write-Host '    "google-analytics": {' -ForegroundColor Gray
Write-Host '      "command": "ga"' -ForegroundColor Gray
Write-Host '    }' -ForegroundColor Gray
Write-Host '  }' -ForegroundColor Gray
Write-Host '}' -ForegroundColor Gray
Write-Host ""
Write-Host "Install location: $InstallDir" -ForegroundColor Cyan
Write-Host "Command line tool: $BinDir" -ForegroundColor Cyan
