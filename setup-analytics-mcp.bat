@echo off
title Google Analytics MCP Setup Tool

echo.
echo ========================================
echo   Google Analytics MCP Setup Tool
echo ========================================
echo.

:: Check if in correct directory
if not exist "complete_reauth.py" (
    echo ERROR: Please run this script from project root directory
    echo Current directory: %CD%
    echo Required file: complete_reauth.py
    pause
    exit /b 1
)

:: Check Python environment
echo [1/6] Checking Python environment...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not installed or not in PATH
    echo Please install Python and add to system PATH
    pause
    exit /b 1
)
echo OK: Python environment check passed

:: Check virtual environment
echo.
echo [2/6] Checking virtual environment...
if exist ".venv\Scripts\activate.bat" (
    echo OK: Found virtual environment: .venv
    call .venv\Scripts\activate.bat
) else (
    echo WARNING: No virtual environment found, using system Python
)

:: Check required packages
echo.
echo [3/6] Checking required Python packages...
python -c "import google.auth, google_auth_oauthlib.flow" >nul 2>&1
if errorlevel 1 (
    echo Missing required Python packages
    echo Installing dependencies...
    pip install google-auth google-auth-oauthlib google-analytics-data google-analytics-admin
    if errorlevel 1 (
        echo ERROR: Package installation failed
        pause
        exit /b 1
    )
)
echo OK: Python packages check passed

:: Check OAuth client secret file
echo.
echo [4/6] Checking OAuth client secret file...
set "CLIENT_SECRET_FILE="
for %%f in (client_secret_*.json) do (
    set "CLIENT_SECRET_FILE=%%f"
    goto :found_secret
)
:found_secret
if "%CLIENT_SECRET_FILE%"=="" (
    echo ERROR: OAuth client secret file not found
    echo Please ensure client_secret_*.json file exists in current directory
    pause
    exit /b 1
)
echo OK: Found client secret file: %CLIENT_SECRET_FILE%

:: Display configuration info
echo.
echo [5/6] Configuration Summary:
echo    Project Directory: %CD%
echo    Python Version: 
python --version
echo    Client Secret: %CLIENT_SECRET_FILE%
echo.

:: Confirm start configuration
echo [6/6] Ready to start Google Analytics MCP authentication
echo.
echo IMPORTANT NOTES:
echo    1. Requires Google account login permissions
echo    2. Requires access to Google Analytics properties
echo    3. Authentication process requires browser operation
echo    4. Please prepare to copy and paste authorization code
echo.
set /p confirm="Start configuration? (y/N): "
if /i not "%confirm%"=="y" (
    echo Configuration cancelled
    pause
    exit /b 0
)

:: Execute authentication script
echo.
echo Starting authentication process...
echo ========================================
python complete_reauth.py
set auth_result=%errorlevel%

echo.
echo ========================================

:: Check authentication result
if %auth_result%==0 (
    echo SUCCESS: Authentication configuration completed!
    echo.
    echo Testing MCP functionality...
    
    :: Test MCP call
    python test_mcp_simple.py
    
    if errorlevel 1 (
        echo WARNING: MCP functionality test failed, but authentication may have succeeded
        echo Please test MCP calls manually
    ) else (
        echo.
        echo COMPLETE SUCCESS! 
        echo Google Analytics MCP is now ready to use!
    )
    
) else (
    echo ERROR: Authentication configuration failed
    echo Please check error messages and retry
)

echo.
echo For more information see: docs\google-analytics-mcp-authentication-guide.md
echo.
pause
