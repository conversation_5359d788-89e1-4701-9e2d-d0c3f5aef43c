{"rustc": 3062648155896360161, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[11207653606310558077, "build_script_build", false, 6460920678865323706]], "local": [{"RerunIfChanged": {"output": "release\\build\\anyhow-9acf4d54b094702f\\output", "paths": ["src/nightly.rs"]}}, {"RerunIfEnvChanged": {"var": "RUSTC_BOOTSTRAP", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}