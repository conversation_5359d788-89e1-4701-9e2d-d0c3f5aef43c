#!/usr/bin/env python3
"""
测试Google Analytics MCP连接的脚本
用于验证MCP服务器是否正常工作
"""

import asyncio
import json
import sys
from pathlib import Path

# 添加项目路径到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from analytics_mcp.coordinator import mcp
    from analytics_mcp.tools.admin import info
    from analytics_mcp.tools.reporting import realtime, core
    print("✅ 成功导入MCP模块")
except ImportError as e:
    print(f"❌ 导入MCP模块失败: {e}")
    sys.exit(1)

async def test_mcp_tools():
    """测试MCP工具是否可用"""
    print("\n🧪 测试MCP工具...")

    try:
        # 获取所有可用工具
        tools = await mcp.list_tools()
        print(f"📋 发现 {len(tools)} 个可用工具:")

        for tool in tools:
            print(f"  - {tool.name}: {tool.description}")

        return len(tools) > 0
    except Exception as e:
        print(f"❌ 获取工具列表失败: {e}")
        return False

async def test_google_auth():
    """测试Google认证"""
    print("\n🔑 测试Google认证...")
    
    try:
        from google.auth import default
        credentials, project = default()
        print(f"✅ 认证成功，项目ID: {project}")
        return True
    except Exception as e:
        print(f"❌ 认证失败: {e}")
        return False

async def test_analytics_connection():
    """测试Google Analytics连接"""
    print("\n📊 测试Google Analytics连接...")
    
    try:
        # 尝试调用get_account_summaries工具
        result = await mcp.call_tool("get_account_summaries_analytics", {})
        print("✅ 成功连接Google Analytics")
        print(f"📈 获取到 {len(result)} 个账户摘要")
        return True
    except Exception as e:
        print(f"❌ 连接Google Analytics失败: {e}")
        return False

def create_gemini_config():
    """创建Gemini CLI配置文件"""
    print("\n⚙️ 创建Gemini CLI配置文件...")
    
    config = {
        "mcpServers": {
            "google-analytics": {
                "command": "python",
                "args": ["-m", "analytics_mcp.server"],
                "cwd": str(project_root),
                "env": {
                    "GOOGLE_APPLICATION_CREDENTIALS": "C:\\Users\\<USER>\\AppData\\Roaming\\gcloud\\application_default_credentials.json",
                    "GOOGLE_CLOUD_PROJECT": "sound-silicon-469508-a8",
                    "PYTHONPATH": str(project_root)
                }
            }
        }
    }
    
    config_file = project_root / "gemini-mcp-config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 配置文件已创建: {config_file}")
    return config_file

def print_usage_instructions():
    """打印使用说明"""
    print("\n📖 使用说明:")
    print("1. 启动MCP Inspector进行测试:")
    print("   mcp dev official-ga-mcp/analytics_mcp/server.py")
    print()
    print("2. 在Gemini CLI中使用MCP (假设支持):")
    print("   gemini --mcp-config gemini-mcp-config.json \"获取我的Google Analytics数据\"")
    print()
    print("3. 或者设置环境变量:")
    print("   export GEMINI_MCP_CONFIG=gemini-mcp-config.json")
    print("   gemini \"分析我的网站数据\"")

async def main():
    """主测试函数"""
    print("🚀 Google Analytics MCP 连接测试")
    print("=" * 50)
    
    # 测试步骤
    tests = [
        ("MCP工具", test_mcp_tools),
        ("Google认证", test_google_auth),
        ("Analytics连接", test_analytics_connection),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name}测试出错: {e}")
            results[test_name] = False
    
    # 创建配置文件
    config_file = create_gemini_config()
    
    # 打印测试结果
    print("\n📊 测试结果汇总:")
    print("-" * 30)
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    # 总体评估
    all_passed = all(results.values())
    if all_passed:
        print("\n🎉 所有测试通过！MCP配置正确，可以在Gemini CLI中使用。")
        print_usage_instructions()
    else:
        print("\n⚠️ 部分测试失败，请检查配置。")
        print("常见问题解决方案:")
        print("- 确保已安装MCP包: pip install -e ./official-ga-mcp")
        print("- 检查Google认证: gcloud auth application-default login")
        print("- 验证项目ID和权限设置")

if __name__ == "__main__":
    asyncio.run(main())
