@echo off
chcp 65001 >nul
title Google Analytics MCP 人工辅助认证工具

echo.
echo ==========================================
echo   Google Analytics MCP 人工辅助认证工具
echo ==========================================
echo.

:: 激活虚拟环境
if exist ".venv\Scripts\activate.bat" (
    call .venv\Scripts\activate.bat
    echo ✅ 虚拟环境已激活
) else (
    echo ⚠️  使用系统Python环境
)

echo.
echo 📋 认证步骤说明:
echo.
echo 1️⃣  脚本将生成Google认证URL
echo 2️⃣  自动在浏览器中打开认证页面
echo 3️⃣  在浏览器中登录您的Google账户
echo 4️⃣  授权应用访问Google Analytics
echo 5️⃣  复制浏览器显示的授权码
echo 6️⃣  回到此窗口粘贴授权码
echo 7️⃣  等待认证完成和测试
echo.

echo 🔑 需要的权限范围:
echo    - Google Analytics 只读权限
echo    - Google Cloud Platform 权限
echo    - Analytics 用户管理权限
echo.

set /p ready="准备开始认证? (y/N): "
if /i not "%ready%"=="y" (
    echo 认证已取消
    pause
    exit /b 0
)

echo.
echo 🚀 启动认证流程...
echo.
echo ⏳ 请等待认证URL生成...
echo 📱 浏览器将自动打开认证页面
echo 💡 如果浏览器未自动打开，请手动复制URL到浏览器
echo.
echo ========================================

:: 运行认证脚本
python complete_reauth.py

echo ========================================
echo.

:: 检查认证结果
if errorlevel 1 (
    echo ❌ 认证过程中出现错误
    echo.
    echo 🔧 可能的解决方案:
    echo    1. 检查网络连接
    echo    2. 确认Google账户权限
    echo    3. 重新复制完整的授权码
    echo    4. 尝试使用不同的浏览器
    echo.
) else (
    echo ✅ 认证流程完成
    echo.
    echo 🧪 进行最终验证测试...
    
    :: 快速验证测试
    python -c "
try:
    from google.analytics.admin_v1beta import AnalyticsAdminServiceClient
    from google.analytics.data_v1beta import BetaAnalyticsDataClient
    
    # 尝试创建客户端
    admin_client = AnalyticsAdminServiceClient()
    data_client = BetaAnalyticsDataClient()
    
    print('✅ 最终验证通过!')
    print('🎉 Google Analytics MCP 已准备就绪!')
    print('')
    print('现在您可以:')
    print('- 询问AI关于网站数据的任何问题')
    print('- AI将自动调用Google Analytics API')
    print('- 获取实时准确的数据分析')
    
except Exception as e:
    print(f'⚠️  验证测试失败: {e}')
    print('认证可能已完成，但需要等待权限生效')
    print('请稍后重试或检查Google Cloud控制台')
"
)

echo.
echo 📖 使用说明:
echo    现在可以直接询问AI:
echo    - "昨天有多少用户访问了网站?"
echo    - "过去7天的流量趋势如何?"
echo    - "主要流量来源是什么?"
echo    - "现在有多少用户在线?"
echo.
echo 📚 详细文档: docs\google-analytics-mcp-authentication-guide.md
echo.

pause
