# 寸止 - 组件样式测试环境

这是一个独立的测试环境，用于开发和调试组件样式，无需依赖 MCP 功能。

## 🎯 特性

- **真实组件引用**: 直接引用项目中的真实组件，修改即时生效
- **独立运行**: 不依赖 Tauri 和 MCP 功能，纯前端环境
- **完整主题支持**: 支持浅色/深色主题切换，使用真实的主题系统
- **组件测试**: 包含主界面、MCP 弹窗、设置组件等的完整测试
- **样式调试**: 方便调试 UnoCSS 样式和 Naive UI 组件

## 🚀 快速开始

### 启动测试环境

```bash
# 在项目根目录运行
pnpm test:ui
```

测试环境将在 `http://localhost:5174` 启动。

### 构建测试环境

```bash
# 构建静态文件
pnpm test:ui:build
```

构建产物将输出到 `dist-test` 目录。

## 📁 目录结构

```
src/frontend/test/
├── components/           # 测试组件
│   ├── MainLayoutTest.vue    # 主界面测试
│   ├── McpPopupTest.vue      # MCP 弹窗测试
│   ├── ComponentsTest.vue    # 组件库测试
│   └── ThemesTest.vue        # 主题测试
├── TestApp.vue          # 测试应用主组件
├── main.ts              # 测试环境入口
├── index.html           # HTML 模板
├── vite.config.ts       # Vite 配置
└── README.md            # 说明文档
```

## 🧪 测试内容

### 1. 主界面测试 (MainLayoutTest)
- 测试真实的 `MainLayout.vue` 组件
- 主题切换功能
- 设置面板交互
- 标签页切换

### 2. MCP 弹窗测试 (McpPopupTest)
- 测试真实的 `McpPopup.vue` 组件
- 不同类型的请求模板
- Markdown 渲染效果
- 预定义选项交互

### 3. 组件库测试 (ComponentsTest)
- 测试所有设置组件
- Naive UI 基础组件
- 统一的 `size="small"` 效果
- 组件状态监控

### 4. 主题测试 (ThemesTest)
- 真实的主题切换系统
- 颜色调色板展示
- CSS 变量实时值
- 组件主题预览

## 🎨 样式开发

### 修改组件样式
1. 在测试环境中找到对应的组件测试页面
2. 修改 `src/frontend/components/` 中的真实组件
3. 保存后自动热重载，立即看到效果

### 调试主题
1. 修改 `src/frontend/theme/` 中的主题配置
2. 在主题测试页面查看实时效果
3. 使用颜色调色板复制颜色值

### 调试 UnoCSS
1. 修改 `uno.config.ts` 配置
2. 在组件中使用新的样式类
3. 在测试环境中验证效果

## 🔧 配置说明

### Vite 配置
- 端口: 5174 (避免与主应用冲突)
- 自动打开浏览器
- 支持 Vue 3 + TypeScript
- 集成 UnoCSS

### 依赖关系
- 复用主应用的所有依赖
- 使用相同的 Naive UI 配置
- 共享主题和样式系统

## 💡 使用技巧

1. **实时开发**: 保持测试环境运行，修改组件后立即查看效果
2. **主题调试**: 使用主题测试页面快速切换和验证主题效果
3. **组件隔离**: 每个测试页面独立，方便专注于特定组件的开发
4. **状态监控**: 查看组件状态面板了解当前的属性值和事件触发

## 🐛 故障排除

### 端口冲突
如果 5174 端口被占用，修改 `vite.config.ts` 中的端口号。

### 样式不生效
确保 UnoCSS 配置正确，检查 `uno.config.ts` 路径。

### 组件导入错误
确保组件路径正确，相对于 `src/frontend/test/` 目录。

## 📝 开发建议

1. **先在测试环境验证**: 新功能先在测试环境中开发和验证
2. **保持同步**: 确保测试环境的组件引用与主应用保持一致
3. **文档更新**: 添加新的测试内容时更新此文档
4. **性能考虑**: 测试环境仅用于开发，不需要考虑生产环境的性能优化
