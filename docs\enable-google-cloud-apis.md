# Google Cloud APIs 启用教程

## 概述

本教程将指导您如何在 Google Cloud 项目中启用 Google Analytics MCP 所需的各种 API，解决常见的 API 权限和访问问题。

## 必需启用的 API

### 1. Gemini for Google Cloud API
- **API 名称**: `cloudaicompanion.googleapis.com`
- **用途**: 支持 Gemini CLI 与 Google Cloud 的集成
- **必需性**: 使用 Gemini CLI 时必需

### 2. Google Analytics Reporting API
- **API 名称**: `analyticsreporting.googleapis.com`
- **用途**: 访问 Google Analytics 报告数据
- **必需性**: 查询 GA 数据时必需

### 3. Google Analytics Data API
- **API 名称**: `analyticsdata.googleapis.com`
- **用途**: 访问 GA4 数据
- **必需性**: 查询 GA4 数据时必需

## 启用步骤

### 方法一：通过错误信息中的直接链接

当您遇到 API 未启用的错误时，错误信息会提供直接链接：

1. **复制错误信息中的链接**
   ```
   https://console.developers.google.com/apis/api/cloudaicompanion.googleapis.com/overview?project=677865822475
   ```

2. **在浏览器中打开链接**
   - 会直接跳转到对应的 API 页面

3. **点击 "启用" 按钮**
   - 等待 API 启用完成

### 方法二：通过 Google Cloud Console 手动启用

#### 步骤 1：访问 Google Cloud Console
1. 打开 [Google Cloud Console](https://console.cloud.google.com/)
2. 确保选择了正确的项目 (项目 ID: 677865822475)

#### 步骤 2：导航到 APIs & Services
1. 在左侧菜单中点击 "APIs & Services"
2. 选择 "Library"

#### 步骤 3：搜索并启用 API
1. **启用 Gemini for Google Cloud API**
   - 在搜索框中输入 "Gemini for Google Cloud"
   - 点击搜索结果中的 API
   - 点击 "启用" 按钮

2. **启用 Google Analytics Reporting API**
   - 搜索 "Analytics Reporting"
   - 选择 "Google Analytics Reporting API"
   - 点击 "启用"

3. **启用 Google Analytics Data API**
   - 搜索 "Analytics Data"
   - 选择 "Google Analytics Data API"
   - 点击 "启用"

### 方法三：使用 gcloud CLI 命令

如果您安装了 Google Cloud CLI，可以使用命令行启用：

```bash
# 设置项目
gcloud config set project 677865822475

# 启用 Gemini for Google Cloud API
gcloud services enable cloudaicompanion.googleapis.com

# 启用 Google Analytics Reporting API
gcloud services enable analyticsreporting.googleapis.com

# 启用 Google Analytics Data API
gcloud services enable analyticsdata.googleapis.com

# 验证已启用的服务
gcloud services list --enabled
```

## 验证 API 启用状态

### 通过 Google Cloud Console 验证
1. 访问 "APIs & Services" > "Dashboard"
2. 查看已启用的 API 列表
3. 确认以下 API 显示为 "已启用":
   - Gemini for Google Cloud API
   - Google Analytics Reporting API
   - Google Analytics Data API

### 通过 gcloud CLI 验证
```bash
# 检查特定 API 状态
gcloud services list --enabled --filter="name:cloudaicompanion.googleapis.com"
gcloud services list --enabled --filter="name:analyticsreporting.googleapis.com"
gcloud services list --enabled --filter="name:analyticsdata.googleapis.com"
```

### 通过 Gemini CLI 验证
启用 API 后，重新运行 Gemini CLI：
```bash
gemini
```

如果成功，应该不再显示 API 权限错误。

## 常见问题解决

### 1. API 启用后仍然报错
**原因**: API 启用需要时间生效（通常 1-5 分钟）
**解决**: 等待几分钟后重试

### 2. 权限不足错误
**错误**: `You do not have permission to enable this API`
**解决**: 
- 确保您是项目的所有者或编辑者
- 联系项目管理员添加权限

### 3. 项目配额限制
**错误**: `Quota exceeded`
**解决**: 
- 检查项目配额设置
- 申请增加配额限制

### 4. 计费账户问题
**错误**: `Billing account required`
**解决**: 
- 为项目关联有效的计费账户
- 确保计费账户状态正常

## API 使用配额和限制

### Gemini for Google Cloud API
- **免费配额**: 根据 Google Cloud 免费层政策
- **付费使用**: 按使用量计费
- **限制**: 每分钟请求数限制

### Google Analytics APIs
- **免费配额**: 每天 50,000 请求
- **付费使用**: 超出免费配额后按请求计费
- **限制**: 每秒查询数限制

## 监控 API 使用情况

### 通过 Google Cloud Console
1. 访问 "APIs & Services" > "Dashboard"
2. 点击具体的 API 查看使用统计
3. 设置配额警报

### 设置配额警报
1. 访问 "APIs & Services" > "Quotas"
2. 选择要监控的 API
3. 设置使用量警报阈值

## 安全最佳实践

### 1. API Key 管理
- 限制 API Key 的使用范围
- 定期轮换 API Key
- 监控 API Key 使用情况

### 2. 项目权限
- 使用最小权限原则
- 定期审查项目成员权限
- 启用审计日志

### 3. 网络安全
- 限制 API 访问的 IP 地址
- 使用 VPC 网络隔离
- 启用 Cloud Security Command Center

## 故障排除检查清单

启用 API 时如果遇到问题，请检查：

- [ ] 是否选择了正确的 Google Cloud 项目
- [ ] 是否有足够的项目权限（所有者或编辑者）
- [ ] 项目是否关联了有效的计费账户
- [ ] 是否等待了足够的时间让 API 生效
- [ ] 网络连接是否正常
- [ ] 浏览器是否登录了正确的 Google 账户

## 快速启用脚本

### Windows PowerShell 脚本
```powershell
# enable-apis.ps1
param(
    [Parameter(Mandatory=$true)]
    [string]$ProjectId
)

Write-Host "为项目 $ProjectId 启用必要的 API..."

# 设置项目
gcloud config set project $ProjectId

# 启用 API
$apis = @(
    "cloudaicompanion.googleapis.com",
    "analyticsreporting.googleapis.com", 
    "analyticsdata.googleapis.com"
)

foreach ($api in $apis) {
    Write-Host "启用 $api..."
    gcloud services enable $api
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ $api 启用成功" -ForegroundColor Green
    } else {
        Write-Host "❌ $api 启用失败" -ForegroundColor Red
    }
}

Write-Host "验证已启用的服务..."
gcloud services list --enabled --filter="name:(cloudaicompanion.googleapis.com OR analyticsreporting.googleapis.com OR analyticsdata.googleapis.com)"
```

### Linux/macOS Bash 脚本
```bash
#!/bin/bash
# enable-apis.sh

PROJECT_ID=${1:-"677865822475"}

echo "为项目 $PROJECT_ID 启用必要的 API..."

# 设置项目
gcloud config set project $PROJECT_ID

# 启用 API
apis=(
    "cloudaicompanion.googleapis.com"
    "analyticsreporting.googleapis.com"
    "analyticsdata.googleapis.com"
)

for api in "${apis[@]}"; do
    echo "启用 $api..."
    if gcloud services enable $api; then
        echo "✅ $api 启用成功"
    else
        echo "❌ $api 启用失败"
    fi
done

echo "验证已启用的服务..."
gcloud services list --enabled --filter="name:(cloudaicompanion.googleapis.com OR analyticsreporting.googleapis.com OR analyticsdata.googleapis.com)"
```

## 相关链接

- [Google Cloud APIs 文档](https://cloud.google.com/apis/docs)
- [Gemini for Google Cloud API](https://console.developers.google.com/apis/api/cloudaicompanion.googleapis.com)
- [Google Analytics Reporting API](https://console.developers.google.com/apis/api/analyticsreporting.googleapis.com)
- [Google Analytics Data API](https://console.developers.google.com/apis/api/analyticsdata.googleapis.com)
- [Google Cloud 计费文档](https://cloud.google.com/billing/docs)

## 总结

正确启用所需的 Google Cloud APIs 是使用 Google Analytics MCP 的关键步骤。通过本教程，您应该能够：

1. 理解需要启用哪些 API
2. 掌握多种启用 API 的方法
3. 解决常见的 API 启用问题
4. 监控和管理 API 使用情况

完成 API 启用后，您的 Google Analytics MCP 应该能够正常工作。
