# Google Analytics MCP 故障排除指南

## 🚨 常见问题快速诊断

### 问题分类
- 🔐 **认证问题** - 权限不足、令牌过期
- 🔗 **连接问题** - MCP服务器无法启动或连接
- 📊 **API问题** - 配额限制、数据访问错误
- ⚙️ **配置问题** - 文件路径、环境变量错误

## 🔐 认证问题解决

### 问题1：权限不足错误
```
403 Request had insufficient authentication scopes.
[reason: "ACCESS_TOKEN_SCOPE_INSUFFICIENT"]
```

**原因分析**：
- 认证令牌缺少必要的权限范围
- 使用了过期的认证凭据

**解决步骤**：
1. 重新运行认证脚本
   ```bash
   python complete_reauth.py
   ```

2. 确保包含正确的权限范围
   ```python
   SCOPES = [
       'https://www.googleapis.com/auth/analytics.readonly',
       'https://www.googleapis.com/auth/cloud-platform',
       'https://www.googleapis.com/auth/analytics.manage.users.readonly'
   ]
   ```

3. 完成浏览器认证流程

### 问题2：认证文件不存在
```
FileNotFoundError: application_default_credentials.json
```

**解决步骤**：
1. 检查认证文件位置
   ```bash
   # Windows
   dir "%USERPROFILE%\AppData\Roaming\gcloud\application_default_credentials.json"
   
   # macOS/Linux
   ls ~/.config/gcloud/application_default_credentials.json
   ```

2. 如果文件不存在，重新认证
   ```bash
   python complete_reauth.py
   ```

### 问题3：授权码无效
```
Invalid authorization code
```

**解决步骤**：
1. 确保完整复制授权码（无多余空格）
2. 授权码只能使用一次，重新获取
3. 检查浏览器是否正确显示授权码

## 🔗 MCP连接问题解决

### 问题1：MCP服务器未找到
```
analytics-mcp server not found
```

**诊断步骤**：
1. 检查MCP服务器是否安装
   ```bash
   google-analytics-mcp --help
   ```

2. 如果命令不存在，重新安装
   ```bash
   pip install git+https://github.com/googleanalytics/google-analytics-mcp.git
   ```

3. 检查配置文件语法
   ```bash
   python -m json.tool ~/.config/claude/claude_desktop_config.json
   ```

### 问题2：MCP服务器启动失败
```
Failed to start MCP server
```

**解决步骤**：
1. 检查Python环境
   ```bash
   python --version
   which python
   ```

2. 检查依赖包
   ```bash
   pip list | grep google
   ```

3. 测试服务器独立启动
   ```bash
   google-analytics-mcp
   ```

4. 查看详细错误日志
   ```bash
   google-analytics-mcp --verbose
   ```

### 问题3：环境变量配置错误
```
Environment variable not set
```

**解决步骤**：
1. 检查配置文件中的环境变量
   ```json
   {
     "mcpServers": {
       "analytics-mcp": {
         "env": {
           "GOOGLE_APPLICATION_CREDENTIALS": "正确的文件路径",
           "GOOGLE_CLOUD_PROJECT": "正确的项目ID"
         }
       }
     }
   }
   ```

2. 验证文件路径存在
   ```bash
   # Windows路径示例
   "C:\\Users\\<USER>\\AppData\\Roaming\\gcloud\\application_default_credentials.json"
   
   # macOS/Linux路径示例
   "/Users/<USER>/.config/gcloud/application_default_credentials.json"
   ```

## 📊 API问题解决

### 问题1：API配额限制
```
429 Exhausted property tokens per day
```

**解决方案**：
1. **等待配额重置**（24小时）
2. **使用不同的Google Analytics属性**
3. **优化查询频率**
4. **申请更高配额**（企业用户）

**配额管理**：
- 访问 [Google Cloud Console配额页面](https://console.cloud.google.com/apis/api/analyticsdata.googleapis.com/quotas)
- 监控当前使用量
- 设置配额警报

### 问题2：属性访问被拒绝
```
403 User does not have sufficient permissions
```

**解决步骤**：
1. 确认Google账户有Analytics属性访问权限
2. 检查属性ID是否正确
3. 联系Analytics管理员添加权限

### 问题3：API未启用
```
403 Google Analytics Data API has not been used
```

**解决步骤**：
1. 访问 [Google Analytics Data API](https://console.cloud.google.com/apis/library/analyticsdata.googleapis.com)
2. 点击"启用"按钮
3. 同样启用 [Google Analytics Admin API](https://console.cloud.google.com/apis/library/analyticsadmin.googleapis.com)

## ⚙️ 配置问题解决

### 问题1：配置文件路径错误
```
Configuration file not found
```

**正确的配置文件位置**：

**Claude Desktop**：
- Windows: `%APPDATA%\Claude\claude_desktop_config.json`
- macOS: `~/Library/Application Support/Claude/claude_desktop_config.json`
- Linux: `~/.config/claude/claude_desktop_config.json`

**Gemini CLI**：
- 所有平台: `~/.gemini/settings.json`

### 问题2：JSON格式错误
```
JSON decode error
```

**解决步骤**：
1. 验证JSON格式
   ```bash
   python -m json.tool config_file.json
   ```

2. 常见格式错误：
   - 缺少逗号
   - 多余的逗号
   - 引号不匹配
   - 路径中的反斜杠需要转义

3. 正确的配置示例：
   ```json
   {
     "mcpServers": {
       "analytics-mcp": {
         "command": "google-analytics-mcp",
         "args": [],
         "env": {
           "GOOGLE_APPLICATION_CREDENTIALS": "C:\\Users\\<USER>\\AppData\\Roaming\\gcloud\\application_default_credentials.json",
           "GOOGLE_CLOUD_PROJECT": "sound-silicon-469508-a8"
         }
       }
     }
   }
   ```

## 🔧 高级故障排除

### 网络连接问题
```bash
# 测试网络连接
curl -I https://analyticsdata.googleapis.com

# 测试DNS解析
nslookup analyticsdata.googleapis.com
```

### 防火墙问题
确保以下域名可以访问：
- `*.googleapis.com`
- `accounts.google.com`
- `oauth2.googleapis.com`

### 代理设置
如果使用代理，设置环境变量：
```bash
export HTTP_PROXY=http://proxy.company.com:8080
export HTTPS_PROXY=http://proxy.company.com:8080
```

## 🧪 诊断工具

### 1. 完整系统检查脚本
```bash
python -c "
import sys
print(f'Python版本: {sys.version}')

try:
    import google.auth
    print('✅ google-auth 已安装')
except ImportError:
    print('❌ google-auth 未安装')

try:
    from google.analytics.data_v1beta import BetaAnalyticsDataClient
    print('✅ google-analytics-data 已安装')
except ImportError:
    print('❌ google-analytics-data 未安装')

try:
    from google.auth import default
    credentials, project = default()
    print(f'✅ 认证成功，项目: {project}')
except Exception as e:
    print(f'❌ 认证失败: {e}')
"
```

### 2. MCP连接测试
```bash
python -c "
try:
    from analytics_mcp.coordinator import mcp
    tools = mcp.list_tools()
    print(f'✅ MCP服务器正常，可用工具: {len(tools)}')
    for tool in tools:
        print(f'  - {tool.name}')
except Exception as e:
    print(f'❌ MCP服务器错误: {e}')
"
```

## 📞 获取帮助

### 1. 查看日志
- AI客户端通常有详细的日志输出
- 查看MCP服务器的错误信息
- 检查Google Cloud Console的API日志

### 2. 社区支持
- [GitHub Issues](https://github.com/googleanalytics/google-analytics-mcp/issues)
- [Discord频道](https://discord.com/channels/971845904002871346/1398002598665257060)
- Google Analytics帮助中心

### 3. 重新开始
如果问题复杂，建议：
1. 删除所有认证文件
2. 重新运行 `setup-analytics-mcp.bat`
3. 按照完整配置指南重新配置

## ✅ 成功验证清单

配置成功的标志：
- [ ] `python test_mcp_simple.py` 运行成功
- [ ] AI客户端显示 `analytics-mcp` 服务器已连接
- [ ] 询问"显示我的Google Analytics账户"能获得回复
- [ ] 查询具体数据（如昨天用户数）能获得准确结果
- [ ] 没有认证或权限错误

**如果所有项目都勾选，恭喜您配置成功！** 🎉
