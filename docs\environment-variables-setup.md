# Google Analytics MCP 环境变量设置教程

## 概述

本教程将指导您如何为 Google Analytics MCP 设置必要的环境变量，以确保 Gemini CLI 能够正常连接和使用 Google Analytics 服务。

## 必需的环境变量

### 1. GOOGLE_CLOUD_PROJECT
- **用途**: 指定 Google Cloud 项目 ID
- **必需性**: 使用 Google Workspace 账户时必需
- **示例值**: `sound-silicon-469508-a8`

### 2. GEMINI_API_KEY
- **用途**: Gemini API 访问密钥
- **必需性**: 使用 Gemini API 时必需
- **示例值**: `AIzaSyCXgBMvmd6WEw2XyQ9jpWHsTjI8giw3nkA`

### 3. GOOGLE_APPLICATION_CREDENTIALS
- **用途**: Google OAuth 凭证文件路径
- **必需性**: 访问 Google Analytics 数据时必需
- **示例值**: `D:\CLT\DM\google-analytics-mcp\client_secret_xxx.json`

## Windows PowerShell 设置方法

### 临时设置（当前会话有效）

```powershell
# 设置 Google Cloud 项目 ID
$env:GOOGLE_CLOUD_PROJECT = "677865822475"

# 设置 Gemini API Key
$env:GEMINI_API_KEY = "your-gemini-api-key"

# 设置 Google 凭证文件路径
$env:GOOGLE_APPLICATION_CREDENTIALS = "path\to\your\credentials.json"

# 启动 Gemini CLI
gemini
```

### 永久设置（系统级）

```powershell
# 设置系统环境变量
[Environment]::SetEnvironmentVariable("GOOGLE_CLOUD_PROJECT", "your-project-id", "User")
[Environment]::SetEnvironmentVariable("GEMINI_API_KEY", "your-gemini-api-key", "User")
[Environment]::SetEnvironmentVariable("GOOGLE_APPLICATION_CREDENTIALS", "path\to\your\credentials.json", "User")
```

### 批处理脚本设置

创建 `setup-env.bat` 文件：

```batch
@echo off
set GOOGLE_CLOUD_PROJECT=your-project-id
set GEMINI_API_KEY=your-gemini-api-key
set GOOGLE_APPLICATION_CREDENTIALS=path\to\your\credentials.json
gemini
```

## Linux/macOS 设置方法

### 临时设置（当前会话有效）

```bash
# 设置环境变量
export GOOGLE_CLOUD_PROJECT="your-project-id"
export GEMINI_API_KEY="your-gemini-api-key"
export GOOGLE_APPLICATION_CREDENTIALS="path/to/your/credentials.json"

# 启动 Gemini CLI
gemini
```

### 永久设置（添加到 ~/.bashrc 或 ~/.zshrc）

```bash
# 编辑配置文件
nano ~/.bashrc  # 或 ~/.zshrc

# 添加以下内容
export GOOGLE_CLOUD_PROJECT="your-project-id"
export GEMINI_API_KEY="your-gemini-api-key"
export GOOGLE_APPLICATION_CREDENTIALS="path/to/your/credentials.json"

# 重新加载配置
source ~/.bashrc  # 或 source ~/.zshrc
```

## 验证设置

### 检查环境变量是否设置成功

**Windows PowerShell:**
```powershell
echo $env:GOOGLE_CLOUD_PROJECT
echo $env:GEMINI_API_KEY
echo $env:GOOGLE_APPLICATION_CREDENTIALS
```

**Linux/macOS:**
```bash
echo $GOOGLE_CLOUD_PROJECT
echo $GEMINI_API_KEY
echo $GOOGLE_APPLICATION_CREDENTIALS
```

### 测试 Gemini CLI 连接

```bash
gemini
```

成功启动后应该看到：
- "Using: 1 MCP server (ctrl+t to view)"
- 不再显示认证错误
- 可以正常输入消息

## 常见问题解决

### 1. 地理位置限制错误
```
User location is not supported for the API use. (Status: FAILED_PRECONDITION)
```
**解决方案**: 使用 VPN 连接到支持 Gemini API 的地区（美国、欧洲等）

### 2. 凭证文件路径错误
```
Failed to load credentials
```
**解决方案**: 
- 确保凭证文件路径正确
- 使用绝对路径
- 检查文件权限

### 3. 项目 ID 错误
```
This account requires setting the GOOGLE_CLOUD_PROJECT env var
```
**解决方案**: 
- 确保项目 ID 正确
- 检查项目是否启用了必要的 API

## 安全注意事项

1. **不要将 API Key 提交到版本控制系统**
2. **使用 .env 文件时添加到 .gitignore**
3. **定期轮换 API Key**
4. **限制 API Key 的访问权限**

## 完整示例

以下是一个完整的 PowerShell 启动脚本示例：

```powershell
# setup-google-analytics-mcp.ps1

# 设置环境变量
$env:GOOGLE_CLOUD_PROJECT = "sound-silicon-469508-a8"
$env:GEMINI_API_KEY = "AIzaSyCXgBMvmd6WEw2XyQ9jpWHsTjI8giw3nkA"
$env:GOOGLE_APPLICATION_CREDENTIALS = "D:\CLT\DM\google-analytics-mcp\client_secret_677865822475-avm2hbllc79dggr24qondei47gcihnnq.apps.googleusercontent.com.json"

# 验证设置
Write-Host "Environment Variables Set:"
Write-Host "GOOGLE_CLOUD_PROJECT: $env:GOOGLE_CLOUD_PROJECT"
Write-Host "GEMINI_API_KEY: $($env:GEMINI_API_KEY.Substring(0,10))..."
Write-Host "GOOGLE_APPLICATION_CREDENTIALS: $env:GOOGLE_APPLICATION_CREDENTIALS"

# 启动 Gemini CLI
Write-Host "Starting Gemini CLI..."
gemini
```

使用方法：
```powershell
.\setup-google-analytics-mcp.ps1
```
