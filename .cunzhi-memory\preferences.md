# 用户偏好设置

- 用户选择分析www.ideaformer-3d.com (属性ID: 448222575)的Google Analytics数据，需要生成总结性Markdown文档，需要帮助运行，但不需要生成测试脚本和编译
- 用户需要深入分析用户行为路径、对比不同流量来源的转化效果、分析页面性能和热门内容，继续生成总结性Markdown文档
- 用户需要分析竞争对手https://www.creality.com，制作数据可视化图表，生成执行清单和行动计划，分析用户留存和回访数据，继续生成总结性Markdown文档
- 用户要求演示正确的MCP Inspector使用方法，不要生成文档，直接运行和演示
- 用户要求创建全新的完整教程文档，总结MCP Inspector成功运行经验，生成总结性Markdown文档，帮助运行，但不要生成测试脚本和编译
- 用户询问MCP服务是否成功，如何封装，参考cunzhi-main项目的封装方式，了解如何制作exe文件，实现命令行直接调用，不要生成总结性Markdown文档、测试脚本，不要编译，帮助运行
- 用户选择创建Rust包装器（类似cunzhi），要求不生成总结性Markdown文档、测试脚本，不编译，帮助运行
- 用户指出当前封装不够通用，需要配置文件模板和教程，或者让用户输入自动配置，而不是硬编码认证路径和项目ID
