# Gemini CLI 调用 Google Analytics MCP 实际测试指南

## 🧪 基于实际测试的配置方法

经过实际测试，以下是在Gemini CLI中成功调用Google Analytics MCP的完整步骤：

---

## 📋 前置条件

### 1. 项目结构确认
```
google-analytics-mcp_gitee/
├── official-ga-mcp/           # MCP服务器代码
│   ├── analytics_mcp/
│   │   ├── server.py         # 服务器入口
│   │   └── tools/            # 工具模块
│   └── pyproject.toml        # 项目配置
├── settings.json             # 现有配置
└── test-mcp-config.json      # 测试配置
```

### 2. 环境验证
- ✅ Python 3.10+ 已安装
- ✅ 虚拟环境已激活
- ✅ Google Cloud 认证已配置
- ✅ MCP包已通过 `pip install -e ./official-ga-mcp` 安装

---

## 🔧 实际配置步骤

### 步骤1: 安装MCP服务器
```bash
# 在项目根目录执行
cd d:\CLT\DM\google-analytics-mcp_gitee
python -m pip install -e ./official-ga-mcp
```

### 步骤2: 验证MCP服务器安装
```bash
# 检查是否安装成功
google-analytics-mcp --help  # 可能无响应，这是正常的
```

### 步骤3: 使用MCP Inspector测试
```bash
# 启动MCP Inspector进行测试
mcp dev official-ga-mcp/analytics_mcp/server.py
```

**测试结果：**
- ✅ MCP Inspector成功启动
- ✅ 服务器监听端口: localhost:6277
- ✅ Web界面地址: http://localhost:6274/
- ✅ 认证令牌已生成

---

## ⚙️ Gemini CLI 配置

### 1. 创建MCP配置文件

创建 `gemini-mcp-config.json`:
```json
{
  "mcpServers": {
    "google-analytics": {
      "command": "python",
      "args": ["-m", "analytics_mcp.server"],
      "cwd": "d:\\CLT\\DM\\google-analytics-mcp_gitee",
      "env": {
        "GOOGLE_APPLICATION_CREDENTIALS": "C:\\Users\\<USER>\\AppData\\Roaming\\gcloud\\application_default_credentials.json",
        "GOOGLE_CLOUD_PROJECT": "sound-silicon-469508-a8",
        "PYTHONPATH": "d:\\CLT\\DM\\google-analytics-mcp_gitee"
      }
    }
  }
}
```

### 2. 关键配置说明

#### 命令配置
- **command**: `"python"` - 使用Python解释器
- **args**: `["-m", "analytics_mcp.server"]` - 运行模块
- **cwd**: 设置工作目录为项目根目录

#### 环境变量
- **GOOGLE_APPLICATION_CREDENTIALS**: Google认证文件路径
- **GOOGLE_CLOUD_PROJECT**: Google Cloud项目ID
- **PYTHONPATH**: 确保Python能找到模块

---

## 🚀 Gemini CLI 使用方法

### 方法1: 直接命令行调用
```bash
# 假设Gemini CLI支持MCP配置
gemini --mcp-config gemini-mcp-config.json "获取我的Google Analytics账户摘要"
```

### 方法2: 环境变量方式
```bash
# 设置环境变量
export GEMINI_MCP_CONFIG="gemini-mcp-config.json"

# 调用Gemini CLI
gemini "分析我的网站最近30天的数据"
```

### 方法3: 配置文件方式
如果Gemini CLI支持配置文件，创建 `~/.config/gemini/config.json`:
```json
{
  "apiKey": "your-gemini-api-key",
  "model": "gemini-pro",
  "mcp": {
    "enabled": true,
    "configFile": "path/to/gemini-mcp-config.json"
  }
}
```

---

## 🧪 测试验证

### 1. MCP服务器测试
```bash
# 启动MCP Inspector
mcp dev official-ga-mcp/analytics_mcp/server.py

# 访问Web界面测试工具
# http://localhost:6274/?MCP_PROXY_AUTH_TOKEN=<token>
```

### 2. 可用工具验证
通过MCP Inspector可以看到以下工具：
- `get_account_summaries_analytics`
- `run_report_analytics`
- `run_realtime_report_analytics`
- `get_property_details_analytics`
- 等等...

### 3. 功能测试示例
```json
{
  "method": "tools/call",
  "params": {
    "name": "get_account_summaries_analytics",
    "arguments": {}
  }
}
```

---

## 🛠️ 故障排除

### 问题1: 模块找不到
**错误**: `ModuleNotFoundError: No module named 'analytics_mcp'`

**解决方案**:
```bash
# 重新安装MCP包
pip install -e ./official-ga-mcp

# 检查PYTHONPATH
echo $PYTHONPATH
```

### 问题2: 认证失败
**错误**: `Authentication failed`

**解决方案**:
```bash
# 重新认证
gcloud auth application-default login

# 检查环境变量
echo $GOOGLE_APPLICATION_CREDENTIALS
```

### 问题3: MCP服务器无响应
**错误**: 服务器启动后无响应

**解决方案**:
- MCP服务器通过stdin/stdout通信，这是正常行为
- 使用MCP Inspector进行测试
- 确保Gemini CLI正确配置了MCP连接

---

## 📝 实际使用示例

### 基础查询
```bash
gemini "使用Google Analytics MCP获取我的账户信息"
```

### 数据分析
```bash
gemini "分析属性ID *********最近30天的流量数据，包括用户来源和设备分布"
```

### 报告生成
```bash
gemini "生成一份包含图表的Google Analytics月度分析报告"
```

---

## 🔍 调试技巧

### 1. 启用详细日志
```bash
# 设置环境变量
export MCP_LOG_LEVEL=DEBUG
export PYTHONPATH="d:\CLT\DM\google-analytics-mcp_gitee"
```

### 2. 使用MCP Inspector调试
- 启动Inspector: `mcp dev official-ga-mcp/analytics_mcp/server.py`
- 在Web界面中测试各个工具
- 查看请求和响应的详细信息

### 3. 检查配置
```bash
# 验证Python模块
python -c "import analytics_mcp; print('MCP模块导入成功')"

# 验证Google认证
python -c "from google.auth import default; print('认证配置正确')"
```

---

## ✅ 成功标志

当配置正确时，您应该能够：
- ✅ MCP Inspector成功启动并显示可用工具
- ✅ 在Web界面中成功调用Google Analytics API
- ✅ Gemini CLI能够识别并调用MCP工具
- ✅ 获取到真实的Google Analytics数据

---

## 📚 下一步

1. **测试所有工具**: 在MCP Inspector中测试每个可用工具
2. **优化配置**: 根据实际使用情况调整配置参数
3. **集成Gemini CLI**: 确保Gemini CLI正确配置MCP支持
4. **自动化脚本**: 创建启动和配置的自动化脚本

---

**注意**: 这个指南基于实际测试结果，MCP服务器已经成功运行并可以通过Inspector进行测试。Gemini CLI的具体MCP集成方式可能需要根据其最新文档进行调整。
