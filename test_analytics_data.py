#!/usr/bin/env python3
"""
测试Google Analytics Data API，查询昨天的用户数据
"""

import asyncio
from google.analytics.data_v1beta import BetaAnalyticsDataClient
from google.analytics.data_v1beta.types import (
    RunReportRequest,
    DateRange,
    Dimension,
    Metric
)

async def test_analytics_data():
    """测试Analytics Data API"""
    print("🔍 测试Google Analytics Data API...")
    
    try:
        # 创建客户端
        client = BetaAnalyticsDataClient()
        print("✅ Data API客户端创建成功")
        
        # 使用Google的演示账户属性ID
        # GA4 - Google Merch Shop (Demo Account)
        property_id = "*********"
        
        print(f"📊 查询属性 {property_id} 昨天的用户数据...")
        
        # 创建报告请求
        request = RunReportRequest(
            property=f"properties/{property_id}",
            date_ranges=[DateRange(start_date="yesterday", end_date="yesterday")],
            dimensions=[Dimension(name="date")],
            metrics=[
                Metric(name="activeUsers"),
                Metric(name="sessions"),
                <PERSON><PERSON>(name="screenPageViews")
            ]
        )
        
        # 执行请求
        response = client.run_report(request=request)
        
        print("✅ 数据查询成功!")
        print(f"📈 获取到 {len(response.rows)} 行数据")
        
        # 显示结果
        if response.rows:
            for row in response.rows:
                date_value = row.dimension_values[0].value
                active_users = row.metric_values[0].value
                sessions = row.metric_values[1].value
                page_views = row.metric_values[2].value
                
                print(f"\n📅 日期: {date_value}")
                print(f"👥 活跃用户: {active_users}")
                print(f"🔄 会话数: {sessions}")
                print(f"📄 页面浏览: {page_views}")
        else:
            print("📊 没有找到数据")
            
        return True
        
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        return False

def main():
    """主函数"""
    print("📊 Google Analytics 昨天用户数据查询")
    print("=" * 50)
    
    # 运行异步测试
    success = asyncio.run(test_analytics_data())
    
    if success:
        print("\n🎉 Google Analytics Data API 工作正常！")
        print("\n💡 现在MCP可以使用以下功能:")
        print("- 查询用户数据")
        print("- 生成报告")
        print("- 获取实时数据")
    else:
        print("\n❌ 需要进一步调试API权限问题")

if __name__ == "__main__":
    main()
