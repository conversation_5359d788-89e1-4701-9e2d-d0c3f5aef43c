# Google Analytics MCP 完整使用指南

## 🎯 目标
从零开始配置并成功调用 Google Analytics MCP 服务器获取数据。

## 📋 第一步：环境变量设置

### 方法 1: 临时设置（推荐用于测试）

**在 Windows 命令提示符中：**
```cmd
set GOOGLE_APPLICATION_CREDENTIALS=C:\Users\<USER>\AppData\Roaming\gcloud\application_default_credentials.json
set GOOGLE_CLOUD_PROJECT=sound-silicon-469508-a8
```

**在 PowerShell 中：**
```powershell
$env:GOOGLE_APPLICATION_CREDENTIALS="C:\Users\<USER>\AppData\Roaming\gcloud\application_default_credentials.json"
$env:GOOGLE_CLOUD_PROJECT="sound-silicon-469508-a8"
```

### 方法 2: 永久设置（推荐用于长期使用）

**通过系统设置：**
1. 右键点击"此电脑" → "属性"
2. 点击"高级系统设置"
3. 点击"环境变量"
4. 在"用户变量"中点击"新建"
5. 添加以下变量：
   - 变量名：`GOOGLE_APPLICATION_CREDENTIALS`
   - 变量值：`C:\Users\<USER>\AppData\Roaming\gcloud\application_default_credentials.json`
6. 再次点击"新建"添加：
   - 变量名：`GOOGLE_CLOUD_PROJECT`
   - 变量值：`sound-silicon-469508-a8`

### 验证环境变量设置
```cmd
echo %GOOGLE_APPLICATION_CREDENTIALS%
echo %GOOGLE_CLOUD_PROJECT%
```

**预期输出：**
```
C:\Users\<USER>\AppData\Roaming\gcloud\application_default_credentials.json
sound-silicon-469508-a8
```

## 📋 第二步：MCP 服务器配置

### 1. 创建或更新 settings.json
在项目根目录创建 `settings.json` 文件：

```json
{
  "mcpServers": {
    "analytics-mcp": {
      "command": "google-analytics-mcp",
      "args": [],
      "env": {
        "GOOGLE_APPLICATION_CREDENTIALS": "C:\\Users\\<USER>\\AppData\\Roaming\\gcloud\\application_default_credentials.json",
        "GOOGLE_CLOUD_PROJECT": "sound-silicon-469508-a8"
      }
    }
  }
}
```

### 2. 验证配置文件
```cmd
type settings.json
```

## 📋 第三步：安装和验证 MCP 服务器

### 1. 安装 Google Analytics MCP
```cmd
cd D:\CLT\DM\google-analytics-mcp\official-ga-mcp
pip install -e .
```

### 2. 验证安装
```cmd
google-analytics-mcp --help
```

**如果命令不存在，尝试：**
```cmd
python -m analytics_mcp.server --help
```

## 📋 第四步：认证设置

### 1. 检查凭据文件是否存在
```cmd
dir "C:\Users\<USER>\AppData\Roaming\gcloud\application_default_credentials.json"
```

### 2. 如果文件不存在，重新认证

**如果 `gcloud` 命令不可用，使用完整路径：**
```cmd
"C:\Program Files (x86)\Google\Cloud SDK\google-cloud-sdk\bin\gcloud.cmd" auth application-default login
```

**或者添加到 PATH（推荐）：**
1. 打开系统环境变量设置
2. 在 PATH 中添加：`C:\Program Files (x86)\Google\Cloud SDK\google-cloud-sdk\bin`
3. 重启命令提示符
4. 然后运行：
```cmd
gcloud auth application-default login
```

### 3. 验证认证

**使用完整路径：**
```cmd
"C:\Program Files (x86)\Google\Cloud SDK\google-cloud-sdk\bin\gcloud.cmd" auth application-default print-access-token
```

**或者如果已添加到 PATH：**
```cmd
gcloud auth application-default print-access-token
```

**预期结果：** 返回一个长的访问令牌字符串

## 📋 第五步：测试 MCP 服务器

### 1. 启动 MCP 服务器（测试连接）
```cmd
google-analytics-mcp
```

**预期行为：**
- 服务器启动但不显示输出（正常）
- 按 `Ctrl+C` 停止
- 如果立即报错，检查环境变量

### 2. 创建简单的测试脚本

**创建 `test_mcp_call.py`：**
```python
import os
import sys

# 设置环境变量
os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = r'C:\Users\<USER>\AppData\Roaming\gcloud\application_default_credentials.json'
os.environ['GOOGLE_CLOUD_PROJECT'] = 'sound-silicon-469508-a8'

# 添加路径
sys.path.insert(0, r'D:\CLT\DM\google-analytics-mcp\official-ga-mcp')

try:
    from google.analytics.data_v1beta import BetaAnalyticsDataClient

    print("🔧 正在测试 Google Analytics API 连接...")

    # 创建客户端
    client = BetaAnalyticsDataClient()

    # 测试列出账户（这个不需要特定属性ID）
    from google.analytics.admin_v1beta import AnalyticsAdminServiceClient
    admin_client = AnalyticsAdminServiceClient()

    # 列出账户
    accounts = admin_client.list_accounts()

    print("✅ 成功连接到 Google Analytics API!")
    print("📊 找到的账户:")

    for account in accounts:
        print(f"   • {account.display_name} (ID: {account.name})")

except Exception as e:
    print(f"❌ 连接失败: {e}")
    print("请检查:")
    print("1. 环境变量是否正确设置")
    print("2. 凭据文件是否存在")
    print("3. Google Cloud 认证是否有效")
```

### 3. 运行测试脚本
```cmd
python test_mcp_call.py
```

## 📋 第六步：使用 MCP 客户端调用

### 方法 1: 使用 Claude Desktop

1. **配置 Claude Desktop**
   - 打开 Claude Desktop
   - 进入设置 → MCP Servers
   - 添加配置文件路径：`D:\CLT\DM\google-analytics-mcp\settings.json`

2. **测试调用**
   在 Claude Desktop 中输入：
   ```
   请获取我的 Google Analytics 账户摘要
   ```

### 方法 2: 使用命令行 MCP 客户端

**安装 MCP 客户端：**
```cmd
pip install mcp
```

**创建测试客户端 `mcp_client_test.py`：**
```python
import asyncio
import json
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

async def test_mcp_client():
    # MCP 服务器参数
    server_params = StdioServerParameters(
        command="google-analytics-mcp",
        args=[],
        env={
            "GOOGLE_APPLICATION_CREDENTIALS": r"C:\Users\<USER>\AppData\Roaming\gcloud\application_default_credentials.json",
            "GOOGLE_CLOUD_PROJECT": "sound-silicon-469508-a8"
        }
    )

    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            # 初始化
            await session.initialize()

            # 列出可用工具
            tools = await session.list_tools()
            print("🔧 可用的 MCP 工具:")
            for tool in tools.tools:
                print(f"   • {tool.name}: {tool.description}")

            # 调用获取账户摘要
            try:
                result = await session.call_tool("get_account_summaries_analytics", {})
                print("\n✅ 成功获取账户摘要:")
                print(json.dumps(result.content, indent=2, ensure_ascii=False))
            except Exception as e:
                print(f"❌ 调用失败: {e}")

# 运行测试
if __name__ == "__main__":
    asyncio.run(test_mcp_client())
```

**运行客户端测试：**
```cmd
python mcp_client_test.py
```

### 方法 3: 直接 Python API 调用（验证底层功能）

**创建 `direct_api_test.py`：**
```python
import os
from google.analytics.data_v1beta import BetaAnalyticsDataClient
from google.analytics.data_v1beta.types import RunReportRequest, DateRange, Dimension, Metric

# 设置环境变量
os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = r'C:\Users\<USER>\AppData\Roaming\gcloud\application_default_credentials.json'
os.environ['GOOGLE_CLOUD_PROJECT'] = 'sound-silicon-469508-a8'

print("🔧 正在测试直接 API 调用...")

try:
    # 创建客户端
    client = BetaAnalyticsDataClient()

    # 测试获取报告（使用 Demo Account 的属性）
    request = RunReportRequest(
        property="properties/*********",  # GA4 - Google Merch Shop
        date_ranges=[DateRange(start_date="7daysAgo", end_date="yesterday")],
        dimensions=[Dimension(name="date")],
        metrics=[Metric(name="sessions")]
    )

    response = client.run_report(request=request)
    print("✅ API 调用成功!")
    print(f"📊 获取到 {len(response.rows)} 行数据")

    # 显示前几行数据
    for i, row in enumerate(response.rows[:3]):
        date_value = row.dimension_values[0].value
        sessions_value = row.metric_values[0].value
        print(f"   {date_value}: {sessions_value} 会话")

except Exception as e:
    print(f"❌ API 调用失败: {e}")
    if "429" in str(e):
        print("ℹ️  这是配额限制错误，表示 API 连接正常但今日配额已用完")
    elif "403" in str(e):
        print("ℹ️  这是权限错误，请检查账户权限")
```

**运行直接 API 测试：**
```cmd
python direct_api_test.py
```

## 📋 第七步：验证成功

### ✅ 成功标志检查清单

**基础配置：**
- [ ] 环境变量正确设置并可验证
- [ ] `settings.json` 文件配置正确
- [ ] 凭据文件存在且有效
- [ ] `google-analytics-mcp` 命令可以启动

**API 连接：**
- [ ] `gcloud auth application-default print-access-token` 返回令牌
- [ ] 直接 API 调用成功（或返回合理错误如配额限制）
- [ ] MCP 客户端可以连接到服务器

**数据获取：**
- [ ] 可以获取账户摘要
- [ ] 可以获取属性信息
- [ ] API 调用返回预期的数据结构

## 🚨 故障排除指南

### 问题 1: 环境变量未设置
**症状：**
```
GOOGLE_APPLICATION_CREDENTIALS is not set
```

**解决步骤：**
1. 重新设置环境变量：
   ```cmd
   set GOOGLE_APPLICATION_CREDENTIALS=C:\Users\<USER>\AppData\Roaming\gcloud\application_default_credentials.json
   set GOOGLE_CLOUD_PROJECT=sound-silicon-469508-a8
   ```
2. 验证设置：
   ```cmd
   echo %GOOGLE_APPLICATION_CREDENTIALS%
   ```

### 问题 2: gcloud 命令未找到
**症状：**
```
'gcloud' is not recognized as an internal or external command
```

**解决步骤：**
1. 使用完整路径：
   ```cmd
   "C:\Program Files (x86)\Google\Cloud SDK\google-cloud-sdk\bin\gcloud.cmd" auth application-default login
   ```
2. 或者添加到系统 PATH：
   - 打开"系统属性" → "环境变量"
   - 在 PATH 中添加：`C:\Program Files (x86)\Google\Cloud SDK\google-cloud-sdk\bin`
   - 重启命令提示符

### 问题 3: 凭据文件问题
**症状：**
```
The file does not have a valid type
```

**解决步骤：**
1. 重新认证（使用完整路径）：
   ```cmd
   "C:\Program Files (x86)\Google\Cloud SDK\google-cloud-sdk\bin\gcloud.cmd" auth application-default login
   ```
2. 在浏览器中选择正确的 Google 账户
3. 完成授权流程
4. 验证文件生成：
   ```cmd
   dir "C:\Users\<USER>\AppData\Roaming\gcloud\application_default_credentials.json"
   ```

### 问题 3: MCP 服务器安装问题
**症状：**
```
'google-analytics-mcp' is not recognized
```

**解决步骤：**
1. 重新安装：
   ```cmd
   cd D:\CLT\DM\google-analytics-mcp\official-ga-mcp
   pip install -e .
   ```
2. 验证安装：
   ```cmd
   pip list | findstr analytics
   ```
3. 如果仍然失败，使用完整路径：
   ```cmd
   python -m analytics_mcp.server
   ```

### 问题 4: API 配额或权限错误
**症状：**
```
429 Exhausted property tokens per day
403 Permission denied
```

**解决方案：**
- **429 错误：** 这实际上是好消息！表示 API 连接正常，只是配额用完了
- **403 错误：** 检查 Google 账户是否有访问 Google Analytics 的权限
- 使用 Demo Account 属性进行测试：`properties/*********`

## 🎯 最终验证步骤

**运行完整验证脚本：**

创建 `final_verification.py`：
```python
import os
import subprocess
import sys

print("🔧 Google Analytics MCP 最终验证")
print("=" * 50)

# 1. 检查环境变量
print("1. 检查环境变量...")
creds = os.environ.get('GOOGLE_APPLICATION_CREDENTIALS')
project = os.environ.get('GOOGLE_CLOUD_PROJECT')

if creds and project:
    print(f"✅ GOOGLE_APPLICATION_CREDENTIALS: {creds}")
    print(f"✅ GOOGLE_CLOUD_PROJECT: {project}")
else:
    print("❌ 环境变量未设置")
    sys.exit(1)

# 2. 检查凭据文件
print("\n2. 检查凭据文件...")
if os.path.exists(creds):
    print("✅ 凭据文件存在")
else:
    print("❌ 凭据文件不存在")
    sys.exit(1)

# 3. 测试 gcloud 认证
print("\n3. 测试 Google Cloud 认证...")
try:
    result = subprocess.run(['gcloud', 'auth', 'application-default', 'print-access-token'],
                          capture_output=True, text=True, timeout=10)
    if result.returncode == 0:
        print("✅ Google Cloud 认证成功")
    else:
        print(f"❌ 认证失败: {result.stderr}")
        sys.exit(1)
except Exception as e:
    print(f"❌ 认证测试失败: {e}")
    sys.exit(1)

# 4. 测试 MCP 服务器
print("\n4. 测试 MCP 服务器...")
try:
    result = subprocess.run(['google-analytics-mcp', '--help'],
                          capture_output=True, text=True, timeout=5)
    print("✅ MCP 服务器命令可用")
except subprocess.TimeoutExpired:
    print("✅ MCP 服务器启动正常（超时是预期的）")
except FileNotFoundError:
    print("❌ MCP 服务器命令未找到")
    print("请运行: pip install -e ./official-ga-mcp")
    sys.exit(1)

print("\n🎉 所有验证通过！Google Analytics MCP 已准备就绪！")
print("\n📋 下一步：")
print("1. 在 Claude Desktop 中配置 MCP 服务器")
print("2. 或使用 Python 客户端进行 API 调用")
print("3. 开始分析你的 Google Analytics 数据！")
```

**运行验证：**
```cmd
python final_verification.py
```

## 🎉 成功！

如果所有步骤都通过，恭喜你！你的 Google Analytics MCP 服务器现在已经完全配置并可以使用了！

**你现在可以：**
- 📊 获取 Google Analytics 账户和属性信息
- 📈 生成各种数据报告
- 🔍 分析网站流量和用户行为
- 🚀 在任何支持 MCP 的客户端中使用这些功能
