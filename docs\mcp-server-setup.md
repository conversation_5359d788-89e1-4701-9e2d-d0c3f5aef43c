# Google Analytics MCP 服务器运行指南

## 🎯 目标

本指南详细说明如何让Google Analytics MCP服务器运行起来，使AI助手能够直接调用Google Analytics API。

## 🔧 MCP服务器架构

```
AI客户端 (Claude Desktop/Gemini CLI)
    ↓ MCP协议通信
Google Analytics MCP服务器
    ↓ API调用
Google Analytics API
    ↓ 数据返回
您的Google Analytics数据
```

## 📋 前置条件检查

### 1. Python环境
```bash
python --version
# 输出: Python 3.8+ (推荐3.10+)
```

### 2. 必要的Python包
```bash
pip list | grep google
# 应该看到:
# google-analytics-admin
# google-analytics-data  
# google-auth
# google-auth-oauthlib
```

### 3. 认证文件
```bash
# Windows
dir "%USERPROFILE%\AppData\Roaming\gcloud\application_default_credentials.json"

# Linux/macOS
ls ~/.config/gcloud/application_default_credentials.json
```

## 🚀 MCP服务器安装方法

### 方法一：从GitHub直接安装（推荐）
```bash
pip install git+https://github.com/googleanalytics/google-analytics-mcp.git
```

### 方法二：本地开发安装
```bash
cd official-ga-mcp
pip install -e .
```

### 方法三：使用pipx运行（无需安装）
```bash
pipx run --spec git+https://github.com/googleanalytics/google-analytics-mcp.git google-analytics-mcp
```

## 🔍 验证MCP服务器安装

### 测试命令可用性
```bash
google-analytics-mcp --help
```

预期输出：
```
Usage: google-analytics-mcp [OPTIONS]
Options:
  --help  Show this message and exit.
```

### 测试服务器启动
```bash
google-analytics-mcp
```

服务器应该启动并等待MCP客户端连接。

## 🔗 AI客户端配置

### Claude Desktop配置

#### Windows配置文件位置
```
%APPDATA%\Claude\claude_desktop_config.json
```

#### macOS配置文件位置
```
~/Library/Application Support/Claude/claude_desktop_config.json
```

#### 配置内容
```json
{
  "mcpServers": {
    "analytics-mcp": {
      "command": "google-analytics-mcp",
      "args": [],
      "env": {
        "GOOGLE_APPLICATION_CREDENTIALS": "C:\\Users\\<USER>\\AppData\\Roaming\\gcloud\\application_default_credentials.json",
        "GOOGLE_CLOUD_PROJECT": "sound-silicon-469508-a8"
      }
    }
  }
}
```

### Gemini CLI配置

#### 配置文件位置
```
~/.gemini/settings.json
```

#### 配置内容
```json
{
  "mcpServers": {
    "analytics-mcp": {
      "command": "pipx",
      "args": [
        "run",
        "--spec",
        "git+https://github.com/googleanalytics/google-analytics-mcp.git",
        "google-analytics-mcp"
      ],
      "env": {
        "GOOGLE_APPLICATION_CREDENTIALS": "/path/to/credentials.json",
        "GOOGLE_CLOUD_PROJECT": "your-project-id"
      }
    }
  }
}
```

## 🧪 MCP服务器测试

### 1. 独立测试MCP服务器
```bash
python -c "
from analytics_mcp.coordinator import mcp
print('MCP服务器模块加载成功')
print(f'可用工具数量: {len(mcp.list_tools())}')
"
```

### 2. 测试API连接
```bash
python test_mcp_simple.py
```

### 3. 测试MCP客户端连接
在AI客户端中输入：
```
/mcp
```

应该看到 `analytics-mcp` 服务器列在可用服务器中。

## 🔄 MCP服务器运行流程

### 1. 服务器启动流程
```
1. AI客户端读取配置文件
2. 启动MCP服务器进程
3. 建立MCP协议连接
4. 服务器加载Google Analytics工具
5. 等待工具调用请求
```

### 2. API调用流程
```
1. 用户向AI提问
2. AI识别需要Google Analytics数据
3. AI调用相应的MCP工具
4. MCP服务器使用认证凭据调用Google Analytics API
5. 返回数据给AI
6. AI分析数据并回答用户
```

## 🛠️ 故障排除

### MCP服务器无法启动

#### 检查Python路径
```bash
which python
which google-analytics-mcp
```

#### 检查依赖包
```bash
pip check
```

#### 重新安装MCP服务器
```bash
pip uninstall google-analytics-mcp
pip install git+https://github.com/googleanalytics/google-analytics-mcp.git
```

### AI客户端无法连接MCP服务器

#### 检查配置文件语法
```bash
# Windows
python -m json.tool "%APPDATA%\Claude\claude_desktop_config.json"

# macOS/Linux  
python -m json.tool ~/.config/claude/claude_desktop_config.json
```

#### 检查环境变量
```bash
echo $GOOGLE_APPLICATION_CREDENTIALS
echo $GOOGLE_CLOUD_PROJECT
```

#### 重启AI客户端
完全关闭并重新启动AI客户端应用程序。

### API调用失败

#### 检查认证状态
```bash
python -c "
from google.auth import default
credentials, project = default()
print(f'项目: {project}')
print(f'认证类型: {type(credentials).__name__}')
"
```

#### 检查API权限
确保认证账户有访问Google Analytics属性的权限。

#### 检查API配额
访问 [Google Cloud Console](https://console.cloud.google.com/apis/api/analyticsdata.googleapis.com/quotas) 检查API配额使用情况。

## 📊 MCP工具使用示例

### 获取账户信息
```python
# AI会自动调用
get_account_summaries_analytics()
```

### 查询报告数据
```python
# AI会自动调用
run_report_analytics(
    property_id="*********",
    date_ranges=[{"start_date": "yesterday", "end_date": "yesterday"}],
    dimensions=["date"],
    metrics=["activeUsers", "sessions"]
)
```

### 获取实时数据
```python
# AI会自动调用
run_realtime_report_analytics(
    property_id="*********",
    dimensions=["country"],
    metrics=["activeUsers"]
)
```

## 🔒 安全注意事项

### 1. 认证文件保护
- 不要将认证文件提交到版本控制
- 设置适当的文件权限
- 定期轮换认证凭据

### 2. API配额管理
- 监控API使用量
- 设置合理的查询频率
- 使用缓存减少API调用

### 3. 权限最小化
- 只授予必要的Analytics权限
- 定期审查访问权限
- 使用专用的服务账户

## 🎉 成功标志

当看到以下情况时，表示MCP服务器运行成功：

1. ✅ AI客户端显示 `analytics-mcp` 服务器已连接
2. ✅ 询问Analytics相关问题时AI能自动调用API
3. ✅ 返回准确的Google Analytics数据
4. ✅ 没有认证或权限错误

**恭喜！您的Google Analytics MCP服务器现在已经成功运行！** 🚀

现在您可以享受AI驱动的Google Analytics数据分析体验了！
