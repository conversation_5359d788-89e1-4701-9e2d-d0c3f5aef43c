# Google Analytics MCP - 封装版本

## 🎯 项目概述

这是Google Analytics MCP的Rust封装版本，参考了[cunzhi项目](https://github.com/imhuso/cunzhi)的封装策略，将Python MCP服务器打包为单个可执行文件，支持直接命令行调用。

## ✨ 特性

- 🚀 **单文件部署** - 打包为单个exe文件，无需复杂配置
- 🔧 **自动环境设置** - 自动配置Google Cloud认证和项目ID
- 📦 **PATH集成** - 安装后可在任意位置调用 `google-analytics-mcp`
- 🛡️ **错误处理** - 完善的错误提示和故障排除
- 🎨 **用户友好** - 清晰的安装和使用指南

## 🚀 快速开始

### 1. 构建和安装

```powershell
# 克隆项目（如果还没有）
git clone <your-repo-url>
cd google-analytics-mcp

# 运行安装脚本
.\install-windows.ps1
```

### 2. 验证安装

```bash
# 重新启动命令提示符后测试
google-analytics-mcp
```

### 3. 配置MCP客户端

在您的MCP客户端配置中添加：

```json
{
  "mcpServers": {
    "google-analytics": {
      "command": "google-analytics-mcp"
    }
  }
}
```

## 🔧 手动构建

如果您想手动构建：

```bash
# 构建发布版本
cargo build --release

# 二进制文件位于
target/release/google-analytics-mcp.exe
```

## 📋 系统要求

- **操作系统**: Windows 10/11
- **Rust**: 1.70+ (构建时需要)
- **Python**: 3.8+ (运行时需要)
- **Google Cloud SDK**: 已配置认证

## 🛠️ 故障排除

### 问题1: 找不到Python
```
❌ 未找到可用的Python命令
```
**解决方案**: 安装Python并确保在PATH中

### 问题2: 认证失败
```
⚠️ 未找到Google Cloud认证文件
```
**解决方案**: 运行 `gcloud auth application-default login`

### 问题3: 找不到脚本文件
```
❌ 未找到 Google Analytics MCP 服务器脚本
```
**解决方案**: 确保 `official-ga-mcp` 目录与exe在同一位置

## 📁 项目结构

```
google-analytics-mcp/
├── src/
│   └── main.rs              # Rust包装器主程序
├── official-ga-mcp/         # Python MCP服务器
├── Cargo.toml              # Rust项目配置
├── install-windows.ps1     # Windows安装脚本
└── README-封装版本.md      # 本文档
```

## 🎉 使用效果

安装完成后，您可以：

1. **直接命令行调用**:
   ```bash
   google-analytics-mcp
   ```

2. **在AI客户端中使用**:
   - 配置MCP服务器后，AI可以直接调用Google Analytics工具
   - 无需每次手动启动或配置环境变量

3. **系统集成**:
   - 添加到PATH，全局可用
   - 自动处理环境变量和认证

## 🔗 相关链接

- [原始Python MCP项目](../official-ga-mcp/)
- [MCP Inspector使用教程](docs/MCP-Inspector-成功运行完整教程.md)
- [cunzhi项目参考](https://github.com/imhuso/cunzhi)
