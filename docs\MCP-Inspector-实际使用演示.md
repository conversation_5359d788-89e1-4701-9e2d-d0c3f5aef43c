# MCP Inspector 实际使用演示指南

## 🎯 演示结果总结

通过Playwright自动化演示，我发现了MCP Inspector的正确使用方法和一些重要注意事项：

---

## 🌐 MCP Inspector 界面详解

### 主要界面组件

**左上角配置区域：**
- **Transport Type**: 默认为STDIO（标准输入输出）
- **Command**: MCP服务器命令（我们设置为`google-analytics-mcp`）
- **Arguments**: 命令参数（可选）
- **Environment Variables**: 环境变量设置
- **Connect按钮**: 连接到MCP服务器

**右侧信息区域：**
- **连接状态**: 显示"Disconnected"或连接错误信息
- **History**: 历史操作记录
- **Server Notifications**: 服务器通知

---

## 🔧 正确的使用方法

### 方法1：通过mcp dev启动（推荐）

这是我们之前成功使用的方法：

```bash
# 1. 设置环境变量
$env:GOOGLE_APPLICATION_CREDENTIALS="C:\Users\<USER>\AppData\Roaming\gcloud\application_default_credentials.json"
$env:GOOGLE_CLOUD_PROJECT="sound-silicon-469508-a8"

# 2. 启动MCP Inspector（自动连接）
mcp dev official-ga-mcp/analytics_mcp/server.py
```

**优势：**
- ✅ 自动配置代理服务器
- ✅ 自动设置认证令牌
- ✅ 自动打开浏览器
- ✅ 直接显示可用工具

### 方法2：手动配置Inspector（复杂）

在Web界面中手动配置连接：

1. **设置Command**: `google-analytics-mcp`
2. **配置环境变量**（需要点击Environment Variables按钮）
3. **点击Connect按钮**

**问题：**
- ❌ 需要手动设置环境变量
- ❌ 可能出现代理冲突
- ❌ 认证配置复杂

---

## 🎭 演示过程中发现的问题

### 问题1：认证令牌问题
**现象：** 访问http://localhost:6274/时出现401错误
**解决：** 必须使用带认证令牌的完整URL

### 问题2：环境变量配置错误
**现象：** 点击Environment Variables按钮时出现JavaScript错误
**原因：** Web界面的环境变量配置功能可能有bug

### 问题3：连接失败
**现象：** 点击Connect后显示"Connection Error"
**原因：** 手动配置的MCP服务器与Inspector的代理服务器冲突

---

## 🚀 推荐的完整使用流程

### 步骤1：启动MCP Inspector
```bash
# 在项目根目录运行
mcp dev official-ga-mcp/analytics_mcp/server.py
```

### 步骤2：访问Web界面
浏览器会自动打开，或手动访问：
```
http://localhost:6274/?MCP_PROXY_AUTH_TOKEN=[认证令牌]
```

### 步骤3：查看可用工具
在左侧面板中，您会看到所有Google Analytics MCP工具：
- `get_account_summaries_analytics`
- `run_report_analytics`
- `run_realtime_report_analytics`
- 等等...

### 步骤4：测试工具
1. 点击左侧的工具名称
2. 在右侧输入参数（JSON格式）
3. 点击"Call Tool"按钮
4. 查看返回结果

---

## 📊 实际操作示例

### 示例1：获取账户摘要
```json
// 工具：get_account_summaries_analytics
// 参数：无需参数
{}
```

### 示例2：运行数据报告
```json
// 工具：run_report_analytics
// 参数：
{
  "property_id": "*********",
  "date_ranges": [{"start_date": "30daysAgo", "end_date": "yesterday"}],
  "dimensions": ["country"],
  "metrics": ["sessions", "totalUsers"]
}
```

### 示例3：实时数据查询
```json
// 工具：run_realtime_report_analytics
// 参数：
{
  "property_id": "*********",
  "dimensions": ["country", "deviceCategory"],
  "metrics": ["activeUsers"]
}
```

---

## 🛠️ 故障排除指南

### 问题：无法访问Web界面
**解决方案：**
1. 确保MCP Inspector正在运行
2. 使用完整的URL（包含认证令牌）
3. 检查端口6274是否被占用

### 问题：工具列表为空
**解决方案：**
1. 确认MCP服务器正确启动
2. 检查环境变量是否设置
3. 验证Google认证是否有效

### 问题：工具调用失败
**解决方案：**
1. 检查参数格式（必须是有效JSON）
2. 验证property_id等参数值
3. 确认API权限和配额

---

## 💡 最佳实践建议

### 1. 使用mcp dev启动
- 避免手动配置的复杂性
- 自动处理认证和代理设置
- 提供最佳的开发体验

### 2. 参数验证
- 使用JSON验证工具检查参数格式
- 从简单参数开始测试
- 参考Google Analytics API文档

### 3. 错误处理
- 仔细阅读错误信息
- 检查API配额使用情况
- 验证账户权限设置

### 4. 开发工作流
1. **在Inspector中测试** → 验证工具和参数
2. **记录成功的调用** → 保存有效参数
3. **应用到Gemini CLI** → 转换为实际使用

---

## 🎉 总结

MCP Inspector是一个强大的调试工具，但需要正确的启动方式：

**✅ 推荐方式：**
```bash
mcp dev official-ga-mcp/analytics_mcp/server.py
```

**❌ 不推荐：**
- 手动在Web界面配置连接
- 尝试同时运行多个MCP服务器实例
- 忽略认证令牌要求

通过正确使用MCP Inspector，您可以：
- 🧪 测试所有Google Analytics MCP工具
- 📊 验证API调用和参数格式
- 🔍 调试连接和认证问题
- 🚀 为Gemini CLI集成做好准备

现在您已经了解了MCP Inspector的正确使用方法，可以开始测试和调试您的Google Analytics MCP工具了！
