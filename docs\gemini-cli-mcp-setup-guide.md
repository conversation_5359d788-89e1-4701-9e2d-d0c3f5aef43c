# Gemini CLI 中配置 Google Analytics MCP 完整指南

## 📋 概述

本指南将帮助您在 Gemini CLI 中成功配置和使用 Google Analytics MCP (Model Context Protocol)，实现与 Google Analytics 数据的无缝集成。

---

## 🔧 环境准备

### 1. 安装必要工具

```bash
# 安装 Gemini CLI (如果尚未安装)
npm install -g @google/generative-ai-cli

# 安装 Google Analytics MCP
pip install google-analytics-mcp

# 或者从源码安装
git clone https://github.com/your-repo/google-analytics-mcp.git
cd google-analytics-mcp
pip install -e .
```

### 2. 验证安装

```bash
# 验证 Gemini CLI
gemini --version

# 验证 Google Analytics MCP
google-analytics-mcp --version
```

---

## 🔑 Google Cloud 认证配置

### 1. 创建服务账号

1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 选择或创建项目
3. 启用 Google Analytics Reporting API
4. 创建服务账号并下载 JSON 密钥文件

### 2. 设置环境变量

```bash
# Windows (PowerShell)
$env:GOOGLE_APPLICATION_CREDENTIALS="C:\path\to\your\service-account-key.json"
$env:GOOGLE_CLOUD_PROJECT="your-project-id"

# Linux/macOS
export GOOGLE_APPLICATION_CREDENTIALS="/path/to/your/service-account-key.json"
export GOOGLE_CLOUD_PROJECT="your-project-id"
```

### 3. 验证认证

```bash
# 测试认证
gcloud auth application-default print-access-token
```

---

## ⚙️ Gemini CLI MCP 配置

### 1. 创建 MCP 配置文件

创建 `~/.config/gemini-cli/mcp-config.json`:

```json
{
  "mcpServers": {
    "google-analytics": {
      "command": "google-analytics-mcp",
      "args": [],
      "env": {
        "GOOGLE_APPLICATION_CREDENTIALS": "/path/to/your/service-account-key.json",
        "GOOGLE_CLOUD_PROJECT": "your-project-id"
      }
    }
  }
}
```

### 2. 更新 Gemini CLI 配置

编辑 `~/.config/gemini-cli/config.json`:

```json
{
  "apiKey": "your-gemini-api-key",
  "model": "gemini-pro",
  "mcp": {
    "enabled": true,
    "configFile": "~/.config/gemini-cli/mcp-config.json"
  }
}
```

### 3. 验证配置

```bash
# 检查 MCP 服务器状态
gemini mcp status

# 列出可用的 MCP 工具
gemini mcp list-tools
```

---

## 🚀 使用示例

### 1. 基础查询

```bash
# 获取账户摘要
gemini "使用 Google Analytics MCP 获取我的账户摘要"

# 获取特定属性的数据
gemini "分析属性 ID ********* 最近 30 天的数据"
```

### 2. 高级分析

```bash
# 流量来源分析
gemini "分析我的网站流量来源，包括 SEO、直接流量和推荐流量的表现"

# 用户行为分析
gemini "分析用户在我的网站上的行为路径，找出转化瓶颈"

# 地理分布分析
gemini "分析我的网站访问者的地理分布，并提供市场拓展建议"
```

### 3. 数据可视化

```bash
# 生成报告
gemini "生成一份包含图表的 Google Analytics 月度报告"

# 创建仪表板
gemini "创建一个实时的 Google Analytics 数据仪表板"
```

---

## 🛠️ 故障排除

### 常见问题及解决方案

#### 1. 认证失败

**错误信息：** `Authentication failed`

**解决方案：**
```bash
# 重新设置认证
gcloud auth application-default login

# 检查环境变量
echo $GOOGLE_APPLICATION_CREDENTIALS
```

#### 2. MCP 服务器无法启动

**错误信息：** `MCP server failed to start`

**解决方案：**
```bash
# 检查 Python 环境
python --version
pip list | grep google-analytics-mcp

# 重新安装 MCP
pip uninstall google-analytics-mcp
pip install google-analytics-mcp
```

#### 3. 权限不足

**错误信息：** `Insufficient permissions`

**解决方案：**
1. 确保服务账号有 Google Analytics 读取权限
2. 在 Google Analytics 中添加服务账号邮箱为查看者
3. 检查 API 是否已启用

#### 4. 配置文件路径问题

**错误信息：** `Config file not found`

**解决方案：**
```bash
# 创建配置目录
mkdir -p ~/.config/gemini-cli

# 检查文件权限
ls -la ~/.config/gemini-cli/
```

---

## 📊 高级配置

### 1. 自定义 MCP 服务器

创建 `custom-analytics-server.py`:

```python
#!/usr/bin/env python3
import asyncio
from mcp.server import Server
from google.analytics.data_v1beta import BetaAnalyticsDataClient

class CustomAnalyticsServer:
    def __init__(self):
        self.client = BetaAnalyticsDataClient()
        self.server = Server("custom-analytics")
    
    async def run_custom_report(self, property_id, dimensions, metrics):
        # 自定义报告逻辑
        pass

if __name__ == "__main__":
    server = CustomAnalyticsServer()
    asyncio.run(server.run())
```

### 2. 批量处理配置

```json
{
  "mcpServers": {
    "google-analytics": {
      "command": "google-analytics-mcp",
      "args": ["--batch-mode", "--cache-enabled"],
      "env": {
        "GOOGLE_APPLICATION_CREDENTIALS": "/path/to/service-account.json",
        "GOOGLE_CLOUD_PROJECT": "your-project-id",
        "ANALYTICS_CACHE_TTL": "3600"
      }
    }
  }
}
```

---

## 🔍 调试技巧

### 1. 启用详细日志

```bash
# 设置日志级别
export MCP_LOG_LEVEL=DEBUG

# 运行 Gemini CLI 并查看详细输出
gemini --verbose "获取 Google Analytics 数据"
```

### 2. 测试 MCP 连接

```bash
# 直接测试 MCP 服务器
google-analytics-mcp --test-connection

# 检查 API 配额
gemini "检查我的 Google Analytics API 配额使用情况"
```

### 3. 性能优化

```json
{
  "mcpServers": {
    "google-analytics": {
      "command": "google-analytics-mcp",
      "args": ["--max-concurrent-requests=5", "--timeout=30"],
      "env": {
        "GOOGLE_APPLICATION_CREDENTIALS": "/path/to/service-account.json",
        "GOOGLE_CLOUD_PROJECT": "your-project-id"
      }
    }
  }
}
```

---

## 📚 最佳实践

### 1. 安全性
- 使用服务账号而非个人账号
- 定期轮换 API 密钥
- 限制服务账号权限范围

### 2. 性能优化
- 启用缓存机制
- 合理设置请求频率限制
- 使用批量请求减少 API 调用

### 3. 监控和维护
- 定期检查 API 配额使用情况
- 监控 MCP 服务器状态
- 及时更新依赖包版本

---

## 🎯 成功验证清单

- [ ] Google Cloud 项目已创建并启用 Analytics API
- [ ] 服务账号已创建并下载密钥文件
- [ ] 环境变量已正确设置
- [ ] Gemini CLI 已安装并配置
- [ ] MCP 配置文件已创建
- [ ] 可以成功获取账户摘要
- [ ] 可以运行基础查询
- [ ] 可以生成分析报告

---

## 📞 支持资源

- [Google Analytics Reporting API 文档](https://developers.google.com/analytics/devguides/reporting/data/v1)
- [Gemini CLI 官方文档](https://ai.google.dev/gemini-api/docs/cli)
- [MCP 协议规范](https://modelcontextprotocol.io/)
- [故障排除指南](./troubleshooting-guide.md)

---

**配置完成后，您就可以在 Gemini CLI 中像在 Augment Code 中一样成功调用 Google Analytics MCP 了！**
