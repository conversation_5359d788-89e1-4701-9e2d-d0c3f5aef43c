# Google Analytics MCP 项目

## 🎯 项目简介

这是一个完整的Google Analytics MCP (Model Context Protocol) 配置项目，让AI助手能够直接查询和分析您的Google Analytics数据。

**一句话描述**：让AI直接回答"昨天有多少用户访问了网站？"这样的问题！

## 🚀 快速开始

### 一键配置（推荐新用户）
```batch
# Windows用户双击运行
setup-analytics-mcp.bat
```

### 仅重新认证（已有配置）
```batch
# Windows用户双击运行
auth-helper.bat
```

## 📚 完整文档

所有详细文档都在 `docs/` 目录中：

### 🎯 必读文档
- **[docs/README.md](docs/README.md)** - 📋 项目完整指南和文档目录
- **[docs/complete-setup-guide.md](docs/complete-setup-guide.md)** - 🚀 从零开始的详细配置步骤

### 🔧 技术文档
- **[docs/mcp-methods-reference.md](docs/mcp-methods-reference.md)** - MCP方法和功能详细参考
- **[docs/mcp-server-setup.md](docs/mcp-server-setup.md)** - MCP服务器运行指南
- **[docs/troubleshooting-guide.md](docs/troubleshooting-guide.md)** - 故障排除指南
- **[docs/usage-examples.md](docs/usage-examples.md)** - 使用示例和最佳实践

## 🎉 配置成功后的体验

### 直接询问AI
```
👤 用户: "昨天有多少用户访问了网站？"

🤖 AI: 根据您的Google Analytics数据：
📅 昨天 (2025-08-19)
👥 活跃用户: 3人
🔄 会话数: 6次  
📄 页面浏览: 5次
相比前一天，用户数增长了50%。
```

### 深度分析
```
👤 用户: "过去7天的流量趋势如何？"

🤖 AI: 📈 过去7天流量分析：
🔥 流量高峰: 8月15日 (7位用户)
📉 流量低谷: 8月16日 (无访问)
📊 平均每日: 4.3位活跃用户
💡 建议: 优化内容发布频率，提高访问稳定性
```

## 📁 项目结构

```
google-analytics-mcp/
├── 📚 docs/                       # 完整文档目录
│   ├── README.md                  # 项目完整指南
│   ├── complete-setup-guide.md    # 详细配置步骤
│   ├── mcp-server-setup.md        # MCP服务器指南
│   ├── usage-examples.md          # 使用示例
│   └── troubleshooting-guide.md   # 故障排除
├── 🏢 official-ga-mcp/            # 官方MCP服务器代码
├── 🔑 client_secret_*.json        # OAuth客户端密钥
├── 🚀 setup-analytics-mcp.bat     # 一键配置脚本
├── 🔧 auth-helper.bat             # 认证助手脚本
├── 🔐 complete_reauth.py          # 认证核心脚本
├── 🧪 test_mcp_simple.py          # 功能测试脚本
└── ⚙️ settings.json               # MCP配置文件
```

## ✅ 配置成功标志

当您看到以下情况时，表示配置成功：

1. ✅ 运行 `python test_mcp_simple.py` 显示成功
2. ✅ AI客户端显示 `analytics-mcp` 服务器已连接
3. ✅ 询问"显示我的Google Analytics账户"能获得回复
4. ✅ 查询具体数据能获得准确结果

## 🔧 核心功能

Google Analytics MCP提供**6个核心工具**，涵盖完整的数据分析需求：

### 🏢 账户和属性管理 (3个工具)
- **`get_account_summaries`** - 获取所有Analytics账户和属性概览
- **`get_property_details`** - 获取属性详细配置信息
- **`list_google_ads_links`** - 查看Google Ads账户关联状态

### 📈 数据报告分析 (2个工具)
- **`run_report`** - 生成多维度数据报告，支持复杂查询
- **`get_custom_dimensions_and_metrics`** - 获取自定义维度和指标

### ⚡ 实时数据监控 (1个工具)
- **`run_realtime_report`** - 获取实时用户活动和网站数据

### 🎯 支持的数据维度
- **时间维度** - 日期、小时、周、月
- **地理维度** - 国家、地区、城市
- **技术维度** - 设备、浏览器、操作系统
- **流量维度** - 来源、媒介、渠道、关键词
- **内容维度** - 页面路径、页面标题、事件名称
- **用户维度** - 新用户/回访用户、年龄、性别

### 📊 支持的数据指标
- **用户指标** - 活跃用户、新用户、回访用户
- **会话指标** - 会话数、跳出率、平均会话时长
- **页面指标** - 页面浏览量、唯一页面浏览量
- **事件指标** - 事件数、转化次数、目标完成数
- **电商指标** - 收入、交易数、平均订单价值

### 🤖 AI智能分析能力
- **自然语言查询** - 直接用中文询问数据问题
- **智能数据解读** - 自动分析趋势、异常和洞察
- **多维度对比** - 自动进行同比、环比分析
- **业务建议** - 基于数据提供优化建议

## 🎯 适用场景

### 👨‍💼 业务分析师
- 快速获取关键指标
- 生成数据报告
- 监控业务表现

### 📈 营销人员
- 评估营销活动效果
- 分析流量来源
- 优化转化漏斗

### 👨‍💻 产品经理
- 监控产品使用情况
- 分析用户行为
- 制定产品策略

### 🏢 企业决策者
- 获取业务洞察
- 监控关键指标
- 支持决策制定

## 🛡️ 安全特性

- 🔐 标准OAuth 2.0认证
- 🔑 安全的凭据管理
- 🛡️ 权限最小化原则
- 🔒 本地数据处理

## 📞 获取帮助

### 📖 文档资源
1. 首先查看 [docs/README.md](docs/README.md) 获取完整指南
2. 遇到问题查看 [故障排除指南](docs/troubleshooting-guide.md)
3. 学习使用方法查看 [使用示例](docs/usage-examples.md)

### 🌐 社区支持
- [GitHub Issues](https://github.com/googleanalytics/google-analytics-mcp/issues)
- [Discord频道](https://discord.com/channels/971845904002871346/1398002598665257060)

### 🔧 技术支持
如果遇到复杂问题：
1. 查看相关文档
2. 运行诊断脚本
3. 提交详细问题报告

## 🏆 项目优势

### 🎯 用户体验
- **一键配置** - 双击即可完成配置
- **详细文档** - 每个步骤都有详细说明
- **故障排除** - 完整的问题解决方案

### 🔧 技术实现
- **标准协议** - 基于MCP标准协议
- **多客户端** - 支持Claude Desktop、Gemini CLI等
- **完整认证** - OAuth 2.0标准认证流程

### 📊 功能完整
- **全API支持** - 支持所有Google Analytics API
- **实时数据** - 支持实时数据查询
- **智能分析** - AI驱动的数据分析

## 🎉 开始使用

1. **下载项目**
   ```bash
   git clone https://github.com/googleanalytics/google-analytics-mcp.git
   cd google-analytics-mcp
   ```

2. **运行配置**
   ```batch
   # 双击运行
   setup-analytics-mcp.bat
   ```

3. **完成认证**
   - 按照提示完成Google账户认证
   - 复制粘贴授权码

4. **开始使用**
   ```
   询问AI: "显示我的Google Analytics数据"
   ```

---

**🚀 开始您的AI驱动的Google Analytics数据分析之旅！**

配置完成后，您将拥有一个强大的AI数据分析助手，随时为您提供准确的网站数据分析！
