#!/usr/bin/env python3
"""
完整的Google Analytics API重新认证脚本
使用正确的权限范围
"""

import os
import json
import webbrowser
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import Flow

# Google Analytics API 所需的权限范围
SCOPES = [
    'https://www.googleapis.com/auth/analytics.readonly',
    'https://www.googleapis.com/auth/cloud-platform',
    'https://www.googleapis.com/auth/analytics.manage.users.readonly'
]

def create_flow():
    """创建OAuth流程"""
    client_secret_file = "client_secret_677865822475-avm2hbllc79dggr24qondei47gcihnnq.apps.googleusercontent.com.json"
    
    if not os.path.exists(client_secret_file):
        print(f"❌ 客户端密钥文件不存在: {client_secret_file}")
        return None
    
    # 创建流程，使用urn:ietf:wg:oauth:2.0:oob作为redirect_uri
    flow = Flow.from_client_secrets_file(
        client_secret_file,
        scopes=SCOPES,
        redirect_uri='urn:ietf:wg:oauth:2.0:oob'
    )
    
    return flow

def main():
    """主函数"""
    print("🔐 Google Analytics API 完整重新认证")
    print("=" * 50)
    print(f"📋 权限范围: {SCOPES}")
    
    # 创建OAuth流程
    flow = create_flow()
    if not flow:
        return
    
    # 获取认证URL
    auth_url, _ = flow.authorization_url(
        prompt='consent',
        access_type='offline',
        include_granted_scopes='true'
    )
    
    print("\n🌐 请按照以下步骤完成认证:")
    print("1. 复制下面的URL到浏览器中打开")
    print("2. 登录您的Google账户")
    print("3. 授权应用访问Google Analytics")
    print("4. 复制授权码并粘贴到下面")
    print("\n📱 认证URL:")
    print(auth_url)
    
    # 尝试自动打开浏览器
    try:
        webbrowser.open(auth_url)
        print("\n✅ 已在浏览器中打开认证页面")
    except:
        print("\n⚠️ 无法自动打开浏览器，请手动复制URL")
    
    # 获取授权码
    print("\n请输入授权码:")
    auth_code = input("授权码: ").strip()
    
    if not auth_code:
        print("❌ 授权码不能为空")
        return
    
    print("🔄 使用授权码获取凭据...")
    try:
        flow.fetch_token(code=auth_code)
        creds = flow.credentials
        
        print("✅ 认证成功！")
        
        # 保存凭据
        creds_dir = os.path.expanduser("~/AppData/Roaming/gcloud")
        os.makedirs(creds_dir, exist_ok=True)
        
        creds_file = os.path.join(creds_dir, "application_default_credentials.json")
        
        # 转换为应用默认凭据格式
        creds_data = {
            "client_id": creds.client_id,
            "client_secret": creds.client_secret,
            "refresh_token": creds.refresh_token,
            "type": "authorized_user",
            "quota_project_id": "sound-silicon-469508-a8",
            "universe_domain": "googleapis.com"
        }
        
        with open(creds_file, 'w') as f:
            json.dump(creds_data, f, indent=2)
        
        print(f"✅ 凭据已保存到: {creds_file}")
        
        # 测试API访问
        print("\n🧪 测试API访问...")
        test_api_access(creds)
        
    except Exception as e:
        print(f"❌ 认证失败: {e}")

def test_api_access(creds):
    """测试API访问"""
    try:
        from google.analytics.data_v1beta import BetaAnalyticsDataClient
        from google.analytics.data_v1beta.types import RunReportRequest, DateRange, Dimension, Metric
        
        # 使用凭据创建客户端
        client = BetaAnalyticsDataClient(credentials=creds)
        
        print("📊 测试Data API...")
        
        # 使用Google演示账户测试
        request = RunReportRequest(
            property="properties/213025502",  # GA4 - Google Merch Shop
            date_ranges=[DateRange(start_date="yesterday", end_date="yesterday")],
            dimensions=[Dimension(name="date")],
            metrics=[Metric(name="activeUsers")]
        )
        
        response = client.run_report(request=request)
        
        print("✅ Data API测试成功！")
        print(f"📈 获取到 {len(response.rows)} 行数据")
        
        if response.rows:
            for row in response.rows:
                date_value = row.dimension_values[0].value
                users = row.metric_values[0].value
                print(f"📅 {date_value}: {users} 活跃用户")
        
        print("\n🎉 Google Analytics MCP 现在可以正常工作了！")
        
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        print("💡 可能需要等待几分钟让权限生效")

if __name__ == "__main__":
    main()
