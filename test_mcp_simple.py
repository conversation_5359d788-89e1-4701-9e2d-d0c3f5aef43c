#!/usr/bin/env python3
"""
简单的MCP功能测试脚本
"""

def test_mcp():
    """测试MCP功能"""
    try:
        print('Testing Google Analytics package import...')
        from google.analytics.admin_v1beta import AnalyticsAdminServiceClient
        from google.analytics.data_v1beta import BetaAnalyticsDataClient
        print('OK: Package import successful')
        
        print('Testing client creation...')
        admin_client = AnalyticsAdminServiceClient()
        data_client = BetaAnalyticsDataClient()
        print('OK: Client creation successful')
        
        print('')
        print('SUCCESS: Google Analytics MCP configuration completed!')
        print('')
        print('Now you can use the following features:')
        print('- Query account and property information')
        print('- Generate data reports')
        print('- Get real-time data')
        print('- Perform data analysis')
        
        return True
        
    except Exception as e:
        print(f'ERROR: Test failed: {e}')
        return False

if __name__ == "__main__":
    import sys
    success = test_mcp()
    sys.exit(0 if success else 1)
