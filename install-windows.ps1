# Google Analytics MCP Windows Install Script

param(
    [switch]$BuildOnly = $false
)

$ErrorActionPreference = "Stop"

Write-Host "Starting Google Analytics MCP installation..." -ForegroundColor Green

# Check required tools
function Test-Command {
    param($Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

Write-Host "Checking required tools..." -ForegroundColor Yellow

if (-not (Test-Command "cargo")) {
    Write-Host "Error: cargo command not found" -ForegroundColor Red
    Write-Host "Please install Rust first: https://rustup.rs/" -ForegroundColor Red
    exit 1
}

if (-not (Test-Command "python")) {
    Write-Host "Error: python command not found" -ForegroundColor Red
    Write-Host "Please install Python first: https://python.org/" -ForegroundColor Red
    exit 1
}

# Check Python dependencies
Write-Host "Checking Python dependencies..." -ForegroundColor Yellow
$RequiredPackages = @("google-analytics-data", "google-auth", "google-cloud-core")

foreach ($Package in $RequiredPackages) {
    try {
        $ImportName = $Package.Replace('-', '_')
        python -c "import $ImportName" 2>$null
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Warning: Python package '$Package' not installed" -ForegroundColor Yellow
            Write-Host "  Please run: pip install $Package" -ForegroundColor Yellow
        }
    }
    catch {
        Write-Host "Cannot check Python package: $Package" -ForegroundColor Yellow
    }
}

# Build binary
Write-Host "Building binary..." -ForegroundColor Yellow
cargo build --release

# Check build result
$BinaryPath = "target\release\google-analytics-mcp.exe"
if (-not (Test-Path $BinaryPath)) {
    Write-Host "Binary build failed: $BinaryPath" -ForegroundColor Red
    exit 1
}

Write-Host "Binary build successful: $BinaryPath" -ForegroundColor Green

# Exit if build-only mode
if ($BuildOnly) {
    Write-Host ""
    Write-Host "Google Analytics MCP build completed!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Binary location: $BinaryPath" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "To install, run the script again without -BuildOnly parameter."
    exit 0
}

# 创建安装目录
$LocalAppData = $env:LOCALAPPDATA
$InstallDir = "$LocalAppData\GoogleAnalyticsMCP"
$BinDir = "$InstallDir\bin"

Write-Host "📁 创建安装目录: $InstallDir" -ForegroundColor Yellow
New-Item -ItemType Directory -Path $BinDir -Force | Out-Null

# 复制二进制文件
$MainExe = "$BinDir\google-analytics-mcp.exe"

Write-Host "📋 安装二进制文件..." -ForegroundColor Yellow
Copy-Item $BinaryPath $MainExe -Force

# 复制Python脚本
$ScriptDir = "$InstallDir\official-ga-mcp"
Write-Host "📋 复制Python脚本..." -ForegroundColor Yellow
if (Test-Path "official-ga-mcp") {
    Copy-Item "official-ga-mcp" $ScriptDir -Recurse -Force
    Write-Host "✅ Python脚本已复制到: $ScriptDir" -ForegroundColor Green
} else {
    Write-Host "⚠️  警告: 未找到 official-ga-mcp 目录" -ForegroundColor Yellow
    Write-Host "   请确保在项目根目录运行此脚本" -ForegroundColor Yellow
}

Write-Host "✅ 二进制文件已安装到: $BinDir" -ForegroundColor Green

# 检查PATH环境变量
$CurrentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
if ($CurrentPath -notlike "*$BinDir*") {
    Write-Host "🔧 添加到用户 PATH 环境变量..." -ForegroundColor Yellow
    
    try {
        $NewPath = if ($CurrentPath) { "$CurrentPath;$BinDir" } else { $BinDir }
        [Environment]::SetEnvironmentVariable("PATH", $NewPath, "User")
        Write-Host "✅ 已添加到 PATH: $BinDir" -ForegroundColor Green
        Write-Host "💡 请重新启动命令提示符或 PowerShell 以使 PATH 生效" -ForegroundColor Cyan
    }
    catch {
        Write-Host "⚠️  无法自动添加到 PATH，请手动添加: $BinDir" -ForegroundColor Yellow
    }
} else {
    Write-Host "✅ PATH 已包含安装目录" -ForegroundColor Green
}

Write-Host ""
Write-Host "🎉 Google Analytics MCP 安装完成！" -ForegroundColor Green
Write-Host ""
Write-Host "📋 使用方法：" -ForegroundColor Cyan
Write-Host "  💻 命令行模式:" -ForegroundColor White
Write-Host "    google-analytics-mcp                - 启动 MCP 服务器" -ForegroundColor White
Write-Host ""
Write-Host "📝 配置 MCP 客户端：" -ForegroundColor Cyan
Write-Host "将以下内容添加到您的 MCP 客户端配置中：" -ForegroundColor White
Write-Host ""
Write-Host @"
{
  "mcpServers": {
    "google-analytics": {
      "command": "google-analytics-mcp"
    }
  }
}
"@ -ForegroundColor Gray
Write-Host ""
Write-Host "📁 安装位置: $InstallDir" -ForegroundColor Cyan
Write-Host "🔗 命令行工具: $BinDir" -ForegroundColor Cyan
Write-Host ""
Write-Host "💡 如果命令行工具无法使用，请重新启动命令提示符或 PowerShell" -ForegroundColor Yellow
